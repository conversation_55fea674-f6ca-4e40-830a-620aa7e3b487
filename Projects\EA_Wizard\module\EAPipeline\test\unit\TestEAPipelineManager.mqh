//+------------------------------------------------------------------+
//|                                        TestEAPipelineManager.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../components/EAPipelineManager.mqh"
#include "../mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| EAPipelineManager測試類別                                        |
//+------------------------------------------------------------------+
class TestEAPipelineManager : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestEAPipelineManager(TestRunner* runner)
    : TestCase("TestEAPipelineManager"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestAddPipeline();
        TestRemovePipeline();
        TestExecutePipeline();
        TestGetPipeline();
        TestGetAllPipelines();
        TestDuplicatePipeline();
        TestExecuteNonExistentPipeline();
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();

        // 測試基本構造函數
        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        m_runner.RecordResult(Assert::AssertNotNull("構造函數_創建對象", manager));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_名稱", "EAPipelineManager", manager.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_類型", "EAPipelineManager", manager.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_初始數量", 0, manager.GetPipelineCount()));

        // 注意：不要刪除單例對象
        TearDown();
    }

    // 測試添加流水線
    void TestAddPipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        MockEAPipeline* pipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("TestPipeline");

        // 添加流水線
        PipelineResult* result = manager.AddPipeline(pipeline);

        m_runner.RecordResult(Assert::AssertNotNull("添加流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("添加流水線_結果成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertEquals("添加流水線_數量增加", 1, manager.GetPipelineCount()));

        // 清理測試數據
        manager.RemovePipelineByName("TestPipeline");
        TearDown();
    }

    // 測試移除流水線
    void TestRemovePipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        MockEAPipeline* pipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("TestPipeline");

        // 先添加流水線
        manager.AddPipeline(pipeline);
        m_runner.RecordResult(Assert::AssertEquals("移除流水線_添加後數量", 1, manager.GetPipelineCount()));

        // 移除流水線
        PipelineResult* result = manager.RemovePipelineByName("TestPipeline");

        m_runner.RecordResult(Assert::AssertNotNull("移除流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("移除流水線_結果成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertEquals("移除流水線_數量減少", 0, manager.GetPipelineCount()));
        TearDown();
    }

    // 測試執行流水線
    void TestExecutePipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        MockEAPipeline* pipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("TestPipeline");

        // 添加流水線
        manager.AddPipeline(pipeline);

        // 執行流水線
        PipelineResult* result = manager.ExecutePipeline("TestPipeline");

        m_runner.RecordResult(Assert::AssertNotNull("執行流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("執行流水線_結果成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertTrue("執行流水線_流水線已執行", pipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("執行流水線_調用次數", 1, pipeline.GetExecuteCallCount()));

        // 清理測試數據
        manager.RemovePipelineByName("TestPipeline");
        TearDown();
    }

    // 測試獲取流水線
    void TestGetPipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        MockEAPipeline* pipeline = MockEAPipelineFactory::CreateSuccessfulPipeline("TestPipeline");

        // 添加流水線
        manager.AddPipeline(pipeline);

        // 獲取流水線
        EAPipelineItem* item = manager.GetPipelineByName("TestPipeline");

        m_runner.RecordResult(Assert::AssertNotNull("獲取流水線_項目不為空", item));

        if(item != NULL)
        {
            IPipeline* retrievedPipeline = item.GetPipeline();
            m_runner.RecordResult(Assert::AssertNotNull("獲取流水線_流水線不為空", retrievedPipeline));

            if(retrievedPipeline != NULL)
            {
                m_runner.RecordResult(Assert::AssertEquals("獲取流水線_名稱匹配", "TestPipeline", retrievedPipeline.GetName()));
            }
            delete item;
        }

        // 清理測試數據
        manager.RemovePipelineByName("TestPipeline");
        TearDown();
    }

    // 測試獲取所有流水線
    void TestGetAllPipelines()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        // 添加多個流水線
        MockEAPipeline* pipeline1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Pipeline1");
        MockEAPipeline* pipeline2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Pipeline2");
        MockEAPipeline* pipeline3 = MockEAPipelineFactory::CreateSuccessfulPipeline("Pipeline3");

        manager.AddPipeline(pipeline1);
        manager.AddPipeline(pipeline2);
        manager.AddPipeline(pipeline3);

        // 檢查數量
        m_runner.RecordResult(Assert::AssertEquals("獲取所有流水線_數量正確", 3, manager.GetPipelineCount()));

        // 清理測試數據
        manager.RemovePipelineByName("Pipeline1");
        manager.RemovePipelineByName("Pipeline2");
        manager.RemovePipelineByName("Pipeline3");

        TearDown();
    }

    // 測試重複流水線
    void TestDuplicatePipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        MockEAPipeline* pipeline1 = MockEAPipelineFactory::CreateSuccessfulPipeline("SameName");
        MockEAPipeline* pipeline2 = MockEAPipelineFactory::CreateSuccessfulPipeline("SameName");

        // 添加第一個流水線應該成功
        PipelineResult* result1 = manager.AddPipeline(pipeline1);
        m_runner.RecordResult(Assert::AssertTrue("重複流水線_第一個成功", result1.IsSuccess()));

        // 添加同名流水線應該失敗
        PipelineResult* result2 = manager.AddPipeline(pipeline2);
        m_runner.RecordResult(Assert::AssertFalse("重複流水線_第二個失敗", result2.IsSuccess()));

        m_runner.RecordResult(Assert::AssertEquals("重複流水線_數量保持", 1, manager.GetPipelineCount()));

        delete result1;
        delete result2;
        delete pipeline2; // pipeline2 沒有被添加到manager中，需要手動刪除

        // 清理測試數據
        manager.RemovePipelineByName("SameName");
        TearDown();
    }

    // 測試執行不存在的流水線
    void TestExecuteNonExistentPipeline()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        // 嘗試執行不存在的流水線
        PipelineResult* result = manager.ExecutePipeline("NonExistentPipeline");

        m_runner.RecordResult(Assert::AssertNotNull("執行不存在流水線_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("執行不存在流水線_結果失敗", result.IsSuccess()));
            m_runner.RecordResult(Assert::AssertContains("執行不存在流水線_錯誤消息", result.GetMessage(), "不存在"));
            delete result;
        }

        TearDown();
    }
};
