//+------------------------------------------------------------------+
//|                                                 RunAllTests.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "TestRunnerAdvanced.mqh"
#include "TestDocumentGenerator.mqh"
#include "TestRunnerWithDocuments.mqh"
#include "unit/TestEACompoundPipeline.mqh"
#include "unit/TestEAPipelineManager.mqh"
#include "integration/TestEAPipelineIntegration.mqh"

//+------------------------------------------------------------------+
//| 運行所有EAPipeline測試                                           |
//+------------------------------------------------------------------+
void RunAllEAPipelineTests()
{
    Print("=== 開始執行 EAPipeline 模組測試套件 ===");

    TestRunner* runner = new TestRunner();

    // 運行單元測試
    Print("--- 開始單元測試 ---");
    RunEAPipelineUnitTests(runner);

    // 運行整合測試
    Print("--- 開始整合測試 ---");
    RunEAPipelineIntegrationTests(runner);

    // 顯示最終摘要
    runner.ShowSummary();

    Print("=== EAPipeline 模組測試套件執行完成 ===");

    delete runner;
}

//+------------------------------------------------------------------+
//| 運行所有EAPipeline測試（增強版）                                 |
//+------------------------------------------------------------------+
void RunAllEAPipelineTestsAdvanced()
{
    Print("=== 開始執行 EAPipeline 模組測試套件（增強版） ===");

    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);

    // 運行單元測試
    Print("--- 開始單元測試 ---");
    RunEAPipelineUnitTests(runner);

    // 運行整合測試
    Print("--- 開始整合測試 ---");
    RunEAPipelineIntegrationTests(runner);

    // 顯示詳細摘要
    runner.ShowDetailedSummary();

    // 顯示失敗測試詳情
    runner.ShowFailedTestsDetail();

    Print("=== EAPipeline 模組測試套件執行完成（增強版） ===");

    delete runner;
}

//+------------------------------------------------------------------+
//| 運行EAPipeline單元測試                                           |
//+------------------------------------------------------------------+
void RunEAPipelineUnitTests(TestRunner* runner = NULL)
{
    bool shouldDeleteRunner = false;
    if(runner == NULL)
    {
        runner = new TestRunner();
        shouldDeleteRunner = true;
        Print("=== 開始執行 EAPipeline 單元測試 ===");
    }

    // 測試EACompoundPipeline
    TestEACompoundPipeline* testCompound = new TestEACompoundPipeline(runner);
    runner.RunTestCase(testCompound);
    delete testCompound;

    // 測試EAPipelineManager
    TestEAPipelineManager* testManager = new TestEAPipelineManager(runner);
    runner.RunTestCase(testManager);
    delete testManager;

    if(shouldDeleteRunner)
    {
        runner.ShowSummary();
        Print("=== EAPipeline 單元測試執行完成 ===");
        delete runner;
    }
}

//+------------------------------------------------------------------+
//| 運行EAPipeline整合測試                                           |
//+------------------------------------------------------------------+
void RunEAPipelineIntegrationTests(TestRunner* runner = NULL)
{
    bool shouldDeleteRunner = false;
    if(runner == NULL)
    {
        runner = new TestRunner();
        shouldDeleteRunner = true;
        Print("=== 開始執行 EAPipeline 整合測試 ===");
    }

    // 測試EAPipeline整合場景
    TestEAPipelineIntegration* testIntegration = new TestEAPipelineIntegration(runner);
    runner.RunTestCase(testIntegration);
    delete testIntegration;

    if(shouldDeleteRunner)
    {
        runner.ShowSummary();
        Print("=== EAPipeline 整合測試執行完成 ===");
        delete runner;
    }
}

//+------------------------------------------------------------------+
//| 運行特定的EAPipeline測試                                         |
//+------------------------------------------------------------------+
void RunSpecificEAPipelineTest(string testName)
{
    Print(StringFormat("=== 開始執行特定測試: %s ===", testName));

    TestRunner* runner = new TestRunner();

    if(testName == "TestEACompoundPipeline")
    {
        TestEACompoundPipeline* test = new TestEACompoundPipeline(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else if(testName == "TestEAPipelineManager")
    {
        TestEAPipelineManager* test = new TestEAPipelineManager(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else if(testName == "TestEAPipelineIntegration")
    {
        TestEAPipelineIntegration* test = new TestEAPipelineIntegration(runner);
        runner.RunTestCase(test);
        delete test;
    }
    else
    {
        Print(StringFormat("未知的測試名稱: %s", testName));
        Print("可用的測試: TestEACompoundPipeline, TestEAPipelineManager, TestEAPipelineIntegration");
    }

    runner.ShowSummary();
    Print(StringFormat("=== 特定測試 %s 執行完成 ===", testName));

    delete runner;
}

//+------------------------------------------------------------------+
//| 快速檢查EAPipeline模組                                           |
//+------------------------------------------------------------------+
bool QuickEAPipelineCheck()
{
    Print("=== 開始 EAPipeline 快速檢查 ===");

    TestRunnerAdvanced* runner = new TestRunnerAdvanced(false); // 不收集詳細信息

    // 運行關鍵測試
    TestEACompoundPipeline* testCompound = new TestEACompoundPipeline(runner);
    runner.RunTestCase(testCompound);
    delete testCompound;

    TestEAPipelineManager* testManager = new TestEAPipelineManager(runner);
    runner.RunTestCase(testManager);
    delete testManager;

    bool allPassed = runner.AllTestsPassed();

    Print(StringFormat("=== EAPipeline 快速檢查完成 - %s ===",
                      allPassed ? "全部通過" : "存在失敗"));

    delete runner;
    return allPassed;
}

//+------------------------------------------------------------------+
//| CI測試 - 持續整合用                                             |
//+------------------------------------------------------------------+
void CIEAPipelineTests()
{
    Print("=== 開始 EAPipeline CI 測試 ===");

    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);

    // 運行所有測試
    RunEAPipelineUnitTests(runner);
    RunEAPipelineIntegrationTests(runner);

    // 檢查結果
    bool allPassed = runner.AllTestsPassed();

    if(allPassed)
    {
        Print("CI測試通過 - 所有測試都成功");
    }
    else
    {
        Print("CI測試失敗 - 存在失敗的測試");
        runner.ShowFailedTestsDetail();
    }

    // 輸出統計信息
    Print(runner.GetTestStatistics());

    Print("=== EAPipeline CI 測試完成 ===");

    delete runner;
}

//+------------------------------------------------------------------+
//| 回歸測試                                                         |
//+------------------------------------------------------------------+
void RunEAPipelineRegressionTests()
{
    Print("=== 開始 EAPipeline 回歸測試 ===");

    TestRunnerAdvanced* runner = new TestRunnerAdvanced(true);

    // 運行所有測試
    RunEAPipelineUnitTests(runner);
    RunEAPipelineIntegrationTests(runner);

    // 詳細分析結果
    runner.ShowDetailedSummary();

    // 檢查特定的回歸點
    TestResultDetail* failedTests[];
    int failedCount = runner.GetFailedTests(failedTests);

    if(failedCount > 0)
    {
        Print(StringFormat("發現 %d 個回歸問題:", failedCount));
        for(int i = 0; i < failedCount; i++)
        {
            if(failedTests[i] != NULL)
            {
                Print(StringFormat("  - %s::%s",
                                 failedTests[i].GetTestClass(),
                                 failedTests[i].GetTestName()));
            }
        }
    }
    else
    {
        Print("沒有發現回歸問題");
    }

    Print("=== EAPipeline 回歸測試完成 ===");

    delete runner;
}

//+------------------------------------------------------------------+
//| 性能測試                                                         |
//+------------------------------------------------------------------+
void RunEAPipelinePerformanceTests()
{
    Print("=== 開始 EAPipeline 性能測試 ===");

    datetime startTime = TimeCurrent();

    // 運行大規模測試
    TestRunnerAdvanced* runner = new TestRunnerAdvanced(false);

    TestEAPipelineIntegration* testIntegration = new TestEAPipelineIntegration(runner);
    runner.RunTestCase(testIntegration);
    delete testIntegration;

    datetime endTime = TimeCurrent();
    int executionTime = (int)(endTime - startTime);

    Print(StringFormat("性能測試完成 - 執行時間: %d秒", executionTime));
    Print(StringFormat("測試統計: %s", runner.GetTestStatistics()));

    if(executionTime > 60) // 如果超過60秒
    {
        Print("警告: 測試執行時間較長，可能存在性能問題");
    }

    Print("=== EAPipeline 性能測試完成 ===");

    delete runner;
}

//+------------------------------------------------------------------+
//| 測試套件信息                                                     |
//+------------------------------------------------------------------+
void ShowEAPipelineTestInfo()
{
    Print("=== EAPipeline 測試套件信息 ===");
    Print("版本: 1.0.0");
    Print("模組: EAPipeline");
    Print("測試類型: 單元測試, 整合測試");
    Print("");
    Print("可用的測試函數:");
    Print("  - RunAllEAPipelineTests()           // 運行所有測試");
    Print("  - RunAllEAPipelineTestsAdvanced()   // 運行所有測試（增強版）");
    Print("  - RunEAPipelineUnitTests()          // 只運行單元測試");
    Print("  - RunEAPipelineIntegrationTests()   // 只運行整合測試");
    Print("  - RunSpecificEAPipelineTest(name)   // 運行特定測試");
    Print("  - QuickEAPipelineCheck()            // 快速檢查");
    Print("  - CIEAPipelineTests()               // CI測試");
    Print("  - RunEAPipelineRegressionTests()    // 回歸測試");
    Print("  - RunEAPipelinePerformanceTests()   // 性能測試");
    Print("");
    Print("測試類別:");
    Print("  - TestEACompoundPipeline            // EACompoundPipeline測試");
    Print("  - TestEAPipelineManager             // EAPipelineManager測試");
    Print("  - TestEAPipelineIntegration         // 整合場景測試");
    Print("=== 信息顯示完成 ===");
}
