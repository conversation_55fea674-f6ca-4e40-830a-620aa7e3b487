//+------------------------------------------------------------------+
//|                                              MockEAPipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../../interface/IPipeline.mqh"
#include "../../../PipelineAdvance/PipelineResult.mqh"

//+------------------------------------------------------------------+
//| Mock EA流水線類別 - 用於測試的模擬EA流水線                        |
//+------------------------------------------------------------------+
class MockEAPipeline : public IPipeline
{
private:
    string m_name;                    // 流水線名稱
    string m_type;                    // 流水線類型
    PipelineResult* m_result;         // 執行結果
    bool m_executed;                  // 是否已執行
    bool m_shouldSucceed;             // 是否應該成功執行
    string m_customMessage;           // 自定義消息
    int m_executeCallCount;           // Execute方法調用次數
    int m_restoreCallCount;           // Restore方法調用次數
    
public:
    // 構造函數
    MockEAPipeline(string name, string type = "MockEAPipeline", bool shouldSucceed = true, string customMessage = "")
    : m_name(name), m_type(type), m_executed(false), m_shouldSucceed(shouldSucceed), 
      m_customMessage(customMessage), m_executeCallCount(0), m_restoreCallCount(0)
    {
        m_result = new PipelineResult(false, "尚未執行", m_name);
    }
    
    // 析構函數
    ~MockEAPipeline()
    {
        if(m_result != NULL)
        {
            delete m_result;
            m_result = NULL;
        }
    }
    
    // 執行流水線
    void Execute() override
    {
        m_executeCallCount++;
        
        if(m_executed)
        {
            // 模擬重複執行的情況
            if(m_result != NULL) delete m_result;
            m_result = new PipelineResult(false, "流水線已經執行過", m_name);
            return;
        }
        
        m_executed = true;
        
        if(m_result != NULL) delete m_result;
        
        if(m_shouldSucceed)
        {
            string message = (m_customMessage != "") ? m_customMessage : "執行成功";
            m_result = new PipelineResult(true, message, m_name);
        }
        else
        {
            string message = (m_customMessage != "") ? m_customMessage : "執行失敗";
            m_result = new PipelineResult(false, message, m_name);
        }
    }
    
    // 獲取流水線名稱
    string GetName() override { return m_name; }
    
    // 獲取流水線類型
    string GetType() override { return m_type; }
    
    // 獲取執行結果
    PipelineResult* GetResult() override { return m_result; }
    
    // 重置流水線狀態
    void Restore() override
    {
        m_restoreCallCount++;
        m_executed = false;
        
        if(m_result != NULL) delete m_result;
        m_result = new PipelineResult(false, "尚未執行", m_name);
    }
    
    // 檢查是否已執行
    bool IsExecuted() override { return m_executed; }
    
    // === Mock特有的方法 ===
    
    // 設置執行結果
    void SetShouldSucceed(bool shouldSucceed) { m_shouldSucceed = shouldSucceed; }
    
    // 設置自定義消息
    void SetCustomMessage(string message) { m_customMessage = message; }
    
    // 獲取Execute方法調用次數
    int GetExecuteCallCount() const { return m_executeCallCount; }
    
    // 獲取Restore方法調用次數
    int GetRestoreCallCount() const { return m_restoreCallCount; }
    
    // 重置調用計數
    void ResetCallCounts()
    {
        m_executeCallCount = 0;
        m_restoreCallCount = 0;
    }
    
    // 手動設置執行狀態（用於測試特殊情況）
    void SetExecuted(bool executed) { m_executed = executed; }
    
    // 手動設置結果（用於測試特殊情況）
    void SetResult(bool success, string message)
    {
        if(m_result != NULL) delete m_result;
        m_result = new PipelineResult(success, message, m_name);
    }
};

//+------------------------------------------------------------------+
//| Mock EA流水線工廠 - 創建各種類型的Mock EA流水線                   |
//+------------------------------------------------------------------+
class MockEAPipelineFactory
{
public:
    // 創建成功的Mock EA流水線
    static MockEAPipeline* CreateSuccessfulPipeline(string name)
    {
        return new MockEAPipeline(name, "SuccessfulMockEA", true, "模擬EA成功執行");
    }
    
    // 創建失敗的Mock EA流水線
    static MockEAPipeline* CreateFailedPipeline(string name)
    {
        return new MockEAPipeline(name, "FailedMockEA", false, "模擬EA執行失敗");
    }
    
    // 創建已執行的Mock EA流水線
    static MockEAPipeline* CreateExecutedPipeline(string name, bool wasSuccessful = true)
    {
        MockEAPipeline* pipeline = new MockEAPipeline(name, "ExecutedMockEA", wasSuccessful);
        pipeline.Execute(); // 立即執行
        return pipeline;
    }
    
    // 創建OnInit類型的Mock流水線
    static MockEAPipeline* CreateOnInitPipeline(string name, bool shouldSucceed = true)
    {
        return new MockEAPipeline(name, "OnInitMockEA", shouldSucceed, "OnInit階段模擬");
    }
    
    // 創建OnTick類型的Mock流水線
    static MockEAPipeline* CreateOnTickPipeline(string name, bool shouldSucceed = true)
    {
        return new MockEAPipeline(name, "OnTickMockEA", shouldSucceed, "OnTick階段模擬");
    }
    
    // 創建OnDeinit類型的Mock流水線
    static MockEAPipeline* CreateOnDeinitPipeline(string name, bool shouldSucceed = true)
    {
        return new MockEAPipeline(name, "OnDeinitMockEA", shouldSucceed, "OnDeinit階段模擬");
    }
    
    // 創建帶有特定消息的Mock流水線
    static MockEAPipeline* CreatePipelineWithMessage(string name, string message, bool shouldSucceed = true)
    {
        return new MockEAPipeline(name, "CustomMockEA", shouldSucceed, message);
    }
    
    // 創建多個Mock流水線
    static int CreateMultiplePipelines(MockEAPipeline* &pipelines[], int count, bool shouldSucceed = true)
    {
        ArrayResize(pipelines, count);
        
        for(int i = 0; i < count; i++)
        {
            string name = StringFormat("MockPipeline_%d", i + 1);
            pipelines[i] = new MockEAPipeline(name, "MultipleMockEA", shouldSucceed);
        }
        
        return count;
    }
    
    // 清理多個Mock流水線
    static void CleanupMultiplePipelines(MockEAPipeline* &pipelines[])
    {
        int size = ArraySize(pipelines);
        for(int i = 0; i < size; i++)
        {
            if(pipelines[i] != NULL)
            {
                delete pipelines[i];
                pipelines[i] = NULL;
            }
        }
        ArrayResize(pipelines, 0);
    }
};
