//+------------------------------------------------------------------+
//|                                        TestEACompoundPipeline.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../components/EACompoundPipeline.mqh"
#include "../mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| EACompoundPipeline測試類別                                       |
//+------------------------------------------------------------------+
class TestEACompoundPipeline : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用
    
public:
    // 構造函數
    TestEACompoundPipeline(TestRunner* runner) 
    : TestCase("TestEACompoundPipeline"), m_runner(runner) {}
    
    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestExecuteEmpty();
        TestExecuteWithChildren();
        TestAddChild();
        TestRemoveChild();
        TestMaxItemsLimit();
        TestRestore();
        TestDuplicateExecution();
        TestGetCountAndMaxItems();
    }
    
private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();
        
        // 測試基本構造函數
        EACompoundPipeline* pipeline = new EACompoundPipeline("TestPipeline");
        
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_創建對象", pipeline));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_名稱", "TestPipeline", pipeline.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_類型", "EACompoundPipeline", pipeline.GetType()));
        m_runner.RecordResult(Assert::AssertFalse("構造函數_未執行", pipeline.IsExecuted()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試基本屬性
    void TestBasicProperties()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("TestPipeline", "CustomType", 50);
        
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_名稱", "TestPipeline", pipeline.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_類型", "CustomType", pipeline.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_最大項目數", 50, pipeline.GetMaxItems()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_當前數量", 0, pipeline.GetCount()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試空流水線執行
    void TestExecuteEmpty()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("EmptyPipeline");
        
        // 執行空流水線
        pipeline.Execute();
        
        m_runner.RecordResult(Assert::AssertTrue("空流水線執行_已執行", pipeline.IsExecuted()));
        
        PipelineResult* result = pipeline.GetResult();
        m_runner.RecordResult(Assert::AssertNotNull("空流水線執行_結果不為空", result));
        
        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("空流水線執行_結果成功", result.IsSuccess()));
        }
        
        delete pipeline;
        TearDown();
    }
    
    // 測試帶子流水線的執行
    void TestExecuteWithChildren()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("ParentPipeline");
        
        // 添加成功的子流水線
        MockEAPipeline* child1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child1");
        MockEAPipeline* child2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child2");
        
        pipeline.AddChild(child1);
        pipeline.AddChild(child2);
        
        // 執行流水線
        pipeline.Execute();
        
        m_runner.RecordResult(Assert::AssertTrue("帶子流水線執行_已執行", pipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("帶子流水線執行_子流水線數量", 2, pipeline.GetCount()));
        
        // 檢查子流水線是否被執行
        m_runner.RecordResult(Assert::AssertTrue("帶子流水線執行_子流水線1已執行", child1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("帶子流水線執行_子流水線2已執行", child2.IsExecuted()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試添加子流水線
    void TestAddChild()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("ParentPipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");
        
        // 添加子流水線
        PipelineResult* result = pipeline.AddChild(child);
        
        m_runner.RecordResult(Assert::AssertNotNull("添加子流水線_結果不為空", result));
        
        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("添加子流水線_結果成功", result.IsSuccess()));
            delete result;
        }
        
        m_runner.RecordResult(Assert::AssertEquals("添加子流水線_數量增加", 1, pipeline.GetCount()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試移除子流水線
    void TestRemoveChild()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("ParentPipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");
        
        // 先添加子流水線
        pipeline.AddChild(child);
        m_runner.RecordResult(Assert::AssertEquals("移除子流水線_添加後數量", 1, pipeline.GetCount()));
        
        // 移除子流水線
        PipelineResult* result = pipeline.RemoveChild("Child");
        
        m_runner.RecordResult(Assert::AssertNotNull("移除子流水線_結果不為空", result));
        
        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("移除子流水線_結果成功", result.IsSuccess()));
            delete result;
        }
        
        m_runner.RecordResult(Assert::AssertEquals("移除子流水線_數量減少", 0, pipeline.GetCount()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試最大項目數限制
    void TestMaxItemsLimit()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("LimitedPipeline", "TestType", 2);
        
        MockEAPipeline* child1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child1");
        MockEAPipeline* child2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child2");
        MockEAPipeline* child3 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child3");
        
        // 添加前兩個子流水線應該成功
        PipelineResult* result1 = pipeline.AddChild(child1);
        PipelineResult* result2 = pipeline.AddChild(child2);
        
        m_runner.RecordResult(Assert::AssertTrue("最大項目限制_第一個成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("最大項目限制_第二個成功", result2.IsSuccess()));
        
        // 添加第三個應該失敗
        PipelineResult* result3 = pipeline.AddChild(child3);
        m_runner.RecordResult(Assert::AssertFalse("最大項目限制_第三個失敗", result3.IsSuccess()));
        
        m_runner.RecordResult(Assert::AssertEquals("最大項目限制_最終數量", 2, pipeline.GetCount()));
        
        delete result1;
        delete result2;
        delete result3;
        delete pipeline;
        delete child3; // child3 沒有被添加到pipeline中，需要手動刪除
        TearDown();
    }
    
    // 測試重置功能
    void TestRestore()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("RestorePipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");
        
        pipeline.AddChild(child);
        pipeline.Execute();
        
        m_runner.RecordResult(Assert::AssertTrue("重置功能_執行前已執行", pipeline.IsExecuted()));
        
        // 重置流水線
        pipeline.Restore();
        
        m_runner.RecordResult(Assert::AssertFalse("重置功能_重置後未執行", pipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("重置功能_子流水線也重置", child.IsExecuted()));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試重複執行防護
    void TestDuplicateExecution()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("DuplicatePipeline");
        MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline("Child");
        
        pipeline.AddChild(child);
        
        // 第一次執行
        pipeline.Execute();
        int firstCallCount = child.GetExecuteCallCount();
        
        // 第二次執行（應該被防護）
        pipeline.Execute();
        int secondCallCount = child.GetExecuteCallCount();
        
        m_runner.RecordResult(Assert::AssertEquals("重複執行防護_調用次數不變", firstCallCount, secondCallCount));
        
        delete pipeline;
        TearDown();
    }
    
    // 測試獲取數量和最大項目數
    void TestGetCountAndMaxItems()
    {
        SetUp();
        
        EACompoundPipeline* pipeline = new EACompoundPipeline("CountPipeline", "TestType", 10);
        
        m_runner.RecordResult(Assert::AssertEquals("數量統計_初始數量", 0, pipeline.GetCount()));
        m_runner.RecordResult(Assert::AssertEquals("數量統計_最大項目數", 10, pipeline.GetMaxItems()));
        
        // 添加幾個子流水線
        for(int i = 0; i < 3; i++)
        {
            MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline(StringFormat("Child%d", i));
            pipeline.AddChild(child);
        }
        
        m_runner.RecordResult(Assert::AssertEquals("數量統計_添加後數量", 3, pipeline.GetCount()));
        
        delete pipeline;
        TearDown();
    }
};
