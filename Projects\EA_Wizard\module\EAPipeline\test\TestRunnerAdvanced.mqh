//+------------------------------------------------------------------+
//|                                          TestRunnerAdvanced.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"

//+------------------------------------------------------------------+
//| 詳細測試結果類 - 用於收集測試的詳細信息                           |
//+------------------------------------------------------------------+
class TestResultDetail
{
private:
    string m_testName;       // 測試名稱
    string m_testClass;      // 測試類別
    bool m_passed;           // 是否通過
    string m_message;        // 消息
    datetime m_startTime;    // 開始時間
    datetime m_endTime;      // 結束時間

public:
    // 構造函數
    TestResultDetail(string testName, string testClass, bool passed, string message = "")
        : m_testName(testName), m_testClass(testClass), m_passed(passed), m_message(message)
    {
        m_startTime = TimeCurrent();
        m_endTime = TimeCurrent();
    }

    // 設置結束時間
    void SetEndTime(datetime endTime) { m_endTime = endTime; }

    // 獲取方法
    string GetTestName() const { return m_testName; }
    string GetTestClass() const { return m_testClass; }
    bool IsPassed() const { return m_passed; }
    string GetMessage() const { return m_message; }
    datetime GetStartTime() const { return m_startTime; }
    datetime GetEndTime() const { return m_endTime; }
    
    // 獲取執行時間（秒）
    int GetExecutionTime() const { return (int)(m_endTime - m_startTime); }
    
    // 獲取格式化的結果字符串
    string GetFormattedResult() const
    {
        string status = m_passed ? "通過" : "失敗";
        string timeStr = TimeToString(m_endTime, TIME_DATE | TIME_MINUTES);
        return StringFormat("[%s] %s::%s - %s (%s)", 
                          status, m_testClass, m_testName, 
                          m_message != "" ? m_message : "無消息", timeStr);
    }
};

//+------------------------------------------------------------------+
//| 增強版測試運行器 - 提供詳細的測試結果收集和分析                   |
//+------------------------------------------------------------------+
class TestRunnerAdvanced : public TestRunner
{
private:
    TestResultDetail* m_results[];   // 詳細測試結果數組
    int m_resultCount;               // 結果數量
    bool m_collectDetails;           // 是否收集詳細信息
    string m_currentTestClass;       // 當前測試類別名稱
    datetime m_testStartTime;        // 測試開始時間
    datetime m_testEndTime;          // 測試結束時間

public:
    // 構造函數
    TestRunnerAdvanced(bool collectDetails = true) 
        : TestRunner(), m_resultCount(0), m_collectDetails(collectDetails), 
          m_currentTestClass(""), m_testStartTime(0), m_testEndTime(0)
    {
        ArrayResize(m_results, 0);
    }

    // 析構函數
    ~TestRunnerAdvanced()
    {
        ClearResults();
    }

    // 記錄測試結果（重寫基類方法）
    virtual void RecordResult(TestResult* result) override
    {
        if(result == NULL) return;

        // 調用基類方法進行基本處理
        TestRunner::RecordResult(result);

        // 如果需要收集詳細信息
        if(m_collectDetails)
        {
            TestResultDetail* detail = new TestResultDetail(
                result.GetTestName(),
                m_currentTestClass,
                result.IsPassed(),
                result.GetMessage()
            );
            
            detail.SetEndTime(TimeCurrent());
            
            // 添加到結果數組
            ArrayResize(m_results, m_resultCount + 1);
            m_results[m_resultCount] = detail;
            m_resultCount++;
        }
    }

    // 運行測試類別（重寫基類方法）
    virtual void RunTestCase(TestCase* testCase) override
    {
        if(testCase == NULL) return;

        m_currentTestClass = testCase.GetClassName();
        m_testStartTime = TimeCurrent();

        Print(StringFormat("=== 開始執行測試類別: %s ===", m_currentTestClass));
        testCase.RunTests();
        
        m_testEndTime = TimeCurrent();
        Print(StringFormat("=== 完成測試類別: %s (耗時: %d秒) ===", 
                          m_currentTestClass, (int)(m_testEndTime - m_testStartTime)));
    }

    // 顯示詳細摘要
    virtual void ShowDetailedSummary()
    {
        ShowSummary(); // 調用基類的摘要方法
        
        if(!m_collectDetails || m_resultCount == 0)
        {
            Print("沒有收集到詳細測試結果");
            return;
        }

        Print("=== 詳細測試結果 ===");
        
        // 按測試類別分組顯示
        string lastClass = "";
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] == NULL) continue;
            
            string currentClass = m_results[i].GetTestClass();
            if(currentClass != lastClass)
            {
                Print(StringFormat("--- %s ---", currentClass));
                lastClass = currentClass;
            }
            
            Print(m_results[i].GetFormattedResult());
        }
    }

    // 獲取失敗的測試
    int GetFailedTests(TestResultDetail* &failedTests[])
    {
        if(!m_collectDetails) return 0;
        
        // 計算失敗測試數量
        int failedCount = 0;
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && !m_results[i].IsPassed())
                failedCount++;
        }
        
        if(failedCount == 0) return 0;
        
        // 分配數組並填充失敗的測試
        ArrayResize(failedTests, failedCount);
        int index = 0;
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && !m_results[i].IsPassed())
            {
                failedTests[index] = m_results[i];
                index++;
            }
        }
        
        return failedCount;
    }

    // 獲取指定測試類別的結果
    int GetResultsByClass(string className, TestResultDetail* &classResults[])
    {
        if(!m_collectDetails) return 0;
        
        // 計算指定類別的測試數量
        int classCount = 0;
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].GetTestClass() == className)
                classCount++;
        }
        
        if(classCount == 0) return 0;
        
        // 分配數組並填充指定類別的測試
        ArrayResize(classResults, classCount);
        int index = 0;
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL && m_results[i].GetTestClass() == className)
            {
                classResults[index] = m_results[i];
                index++;
            }
        }
        
        return classCount;
    }

    // 獲取測試統計信息
    string GetTestStatistics()
    {
        if(!m_collectDetails) return "未收集詳細統計信息";
        
        string stats = StringFormat("總測試數: %d, 通過: %d, 失敗: %d, 成功率: %.2f%%",
                                   GetTotalTests(), GetPassedTests(), GetFailedTests(),
                                   GetTotalTests() > 0 ? (double)GetPassedTests() / GetTotalTests() * 100.0 : 0.0);
        
        // 添加執行時間統計
        if(m_testEndTime > m_testStartTime)
        {
            stats += StringFormat(", 總耗時: %d秒", (int)(m_testEndTime - m_testStartTime));
        }
        
        return stats;
    }

    // 清空結果
    void ClearResults()
    {
        for(int i = 0; i < m_resultCount; i++)
        {
            if(m_results[i] != NULL)
            {
                delete m_results[i];
                m_results[i] = NULL;
            }
        }
        ArrayResize(m_results, 0);
        m_resultCount = 0;
    }

    // 設置是否收集詳細信息
    void SetCollectDetails(bool collect) { m_collectDetails = collect; }

    // 檢查是否收集詳細信息
    bool IsCollectingDetails() const { return m_collectDetails; }

    // 獲取詳細結果數量
    int GetDetailResultCount() const { return m_resultCount; }

    // 顯示失敗測試的詳細信息
    void ShowFailedTestsDetail()
    {
        TestResultDetail* failedTests[];
        int failedCount = GetFailedTests(failedTests);
        
        if(failedCount == 0)
        {
            Print("沒有失敗的測試");
            return;
        }
        
        Print(StringFormat("=== 失敗測試詳情 (%d個) ===", failedCount));
        for(int i = 0; i < failedCount; i++)
        {
            if(failedTests[i] != NULL)
            {
                Print(failedTests[i].GetFormattedResult());
            }
        }
    }

    // 快速檢查（靜默模式）
    bool QuickCheck()
    {
        bool originalCollect = m_collectDetails;
        m_collectDetails = false; // 暫時關閉詳細收集
        
        // 這裡可以運行一些快速測試
        // 實際實現需要根據具體需求添加
        
        m_collectDetails = originalCollect; // 恢復原設置
        return AllTestsPassed();
    }
};
