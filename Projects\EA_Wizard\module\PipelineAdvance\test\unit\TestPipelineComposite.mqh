//+------------------------------------------------------------------+
//|                                       TestPipelineComposite.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../PipelineComposite.mqh"
#include "../mock/MockPipeline.mqh"

//+------------------------------------------------------------------+
//| PipelineComposite測試類別                                        |
//+------------------------------------------------------------------+
class TestPipelineComposite : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用
    
public:
    // 構造函數
    TestPipelineComposite(TestRunner* runner) 
    : TestCase("TestPipelineComposite"), m_runner(runner) {}
    
    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestBasicProperties();
        TestAddChild();
        TestRemoveChild();
        TestExecuteEmpty();
        TestExecuteWithChildren();
        TestMaxItemsLimit();
        TestRestore();
        TestDuplicateExecution();
        TestGetCountAndMaxItems();
    }
    
private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();
        
        // 測試默認構造
        PipelineComposite* composite1 = new PipelineComposite("測試組合", "TestComposite");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_默認參數", composite1));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_名稱", "測試組合", composite1.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_類型", "TestComposite", composite1.GetType()));
        m_runner.RecordResult(Assert::AssertFalse("構造函數_初始未執行", composite1.IsExecuted()));
        
        // 測試帶最大項目數的構造
        PipelineComposite* composite2 = new PipelineComposite("測試組合2", "TestComposite", 50);
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_帶最大項目數", composite2));
        m_runner.RecordResult(Assert::AssertEquals("構造函數_最大項目數", 50, composite2.GetMaxItems()));
        
        // 清理
        delete composite1;
        delete composite2;
        
        TearDown();
    }
    
    // 測試基本屬性
    void TestBasicProperties()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("屬性測試", "PropertyTest", 10);
        
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_名稱", "屬性測試", composite.GetName()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_類型", "PropertyTest", composite.GetType()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_最大項目數", 10, composite.GetMaxItems()));
        m_runner.RecordResult(Assert::AssertEquals("基本屬性_初始計數", 0, composite.GetCount()));
        m_runner.RecordResult(Assert::AssertNotNull("基本屬性_結果不為空", composite.GetResult()));
        
        delete composite;
        
        TearDown();
    }
    
    // 測試添加子流水線
    void TestAddChild()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("添加測試", "AddTest", 3);
        MockPipeline* child1 = new MockPipeline("子流水線1");
        MockPipeline* child2 = new MockPipeline("子流水線2");
        
        // 測試添加第一個子流水線
        PipelineResult* result1 = composite.Add(child1);
        m_runner.RecordResult(Assert::AssertNotNull("添加子流水線_結果1不為空", result1));
        m_runner.RecordResult(Assert::AssertTrue("添加子流水線_結果1成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("添加子流水線_計數1", 1, composite.GetCount()));
        
        // 測試添加第二個子流水線
        PipelineResult* result2 = composite.Add(child2);
        m_runner.RecordResult(Assert::AssertTrue("添加子流水線_結果2成功", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("添加子流水線_計數2", 2, composite.GetCount()));
        
        // 測試添加空指針
        PipelineResult* result3 = composite.Add(NULL);
        m_runner.RecordResult(Assert::AssertFalse("添加子流水線_空指針失敗", result3.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("添加子流水線_空指針後計數不變", 2, composite.GetCount()));
        
        // 清理
        delete composite;
        delete child1;
        delete child2;
        
        TearDown();
    }
    
    // 測試移除子流水線
    void TestRemoveChild()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("移除測試", "RemoveTest");
        MockPipeline* child1 = new MockPipeline("子流水線1");
        MockPipeline* child2 = new MockPipeline("子流水線2");
        
        // 先添加子流水線
        composite.Add(child1);
        composite.Add(child2);
        m_runner.RecordResult(Assert::AssertEquals("移除前_計數", 2, composite.GetCount()));
        
        // 測試移除存在的子流水線
        PipelineResult* result1 = composite.Remove(child1);
        m_runner.RecordResult(Assert::AssertTrue("移除子流水線_成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("移除後_計數", 1, composite.GetCount()));
        
        // 測試移除不存在的子流水線
        MockPipeline* nonExistChild = new MockPipeline("不存在的子流水線");
        PipelineResult* result2 = composite.Remove(nonExistChild);
        m_runner.RecordResult(Assert::AssertFalse("移除不存在_失敗", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("移除不存在後_計數不變", 1, composite.GetCount()));
        
        // 測試移除空指針
        PipelineResult* result3 = composite.Remove(NULL);
        m_runner.RecordResult(Assert::AssertFalse("移除空指針_失敗", result3.IsSuccess()));
        
        // 清理
        delete composite;
        delete child1;
        delete child2;
        delete nonExistChild;
        
        TearDown();
    }
    
    // 測試執行空組合
    void TestExecuteEmpty()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("空執行測試", "EmptyTest");
        
        // 執行空組合應該成功
        composite.Execute();
        
        m_runner.RecordResult(Assert::AssertTrue("空執行_已執行", composite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("空執行_結果成功", composite.GetResult().IsSuccess()));
        
        delete composite;
        
        TearDown();
    }
    
    // 測試執行帶子流水線的組合
    void TestExecuteWithChildren()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("執行測試", "ExecuteTest");
        MockPipeline* child1 = MockPipelineFactory::CreateSuccessfulPipeline("成功子流水線");
        MockPipeline* child2 = MockPipelineFactory::CreateSuccessfulPipeline("成功子流水線2");
        
        composite.Add(child1);
        composite.Add(child2);
        
        // 執行組合
        composite.Execute();
        
        m_runner.RecordResult(Assert::AssertTrue("執行帶子流水線_已執行", composite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("執行帶子流水線_結果成功", composite.GetResult().IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("執行帶子流水線_子流水線1已執行", child1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("執行帶子流水線_子流水線2已執行", child2.IsExecuted()));
        
        // 清理
        delete composite;
        delete child1;
        delete child2;
        
        TearDown();
    }
    
    // 測試最大項目數限制
    void TestMaxItemsLimit()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("限制測試", "LimitTest", 2);
        MockPipeline* child1 = new MockPipeline("子流水線1");
        MockPipeline* child2 = new MockPipeline("子流水線2");
        MockPipeline* child3 = new MockPipeline("子流水線3");
        
        // 添加到限制數量
        composite.Add(child1);
        composite.Add(child2);
        m_runner.RecordResult(Assert::AssertEquals("限制測試_達到限制", 2, composite.GetCount()));
        
        // 嘗試超過限制
        PipelineResult* result = composite.Add(child3);
        m_runner.RecordResult(Assert::AssertFalse("限制測試_超過限制失敗", result.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("限制測試_超過限制後計數不變", 2, composite.GetCount()));
        
        // 清理
        delete composite;
        delete child1;
        delete child2;
        delete child3;
        
        TearDown();
    }
    
    // 測試重置功能
    void TestRestore()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("重置測試", "RestoreTest");
        MockPipeline* child = MockPipelineFactory::CreateSuccessfulPipeline("子流水線");
        
        composite.Add(child);
        composite.Execute();
        
        // 確認已執行
        m_runner.RecordResult(Assert::AssertTrue("重置前_已執行", composite.IsExecuted()));
        
        // 重置
        composite.Restore();
        
        // 確認重置成功
        m_runner.RecordResult(Assert::AssertFalse("重置後_未執行", composite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertFalse("重置後_結果為未執行", composite.GetResult().IsSuccess()));
        
        // 清理
        delete composite;
        delete child;
        
        TearDown();
    }
    
    // 測試重複執行防護
    void TestDuplicateExecution()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("重複執行測試", "DuplicateTest");
        MockPipeline* child = MockPipelineFactory::CreateSuccessfulPipeline("子流水線");
        
        composite.Add(child);
        
        // 第一次執行
        composite.Execute();
        m_runner.RecordResult(Assert::AssertTrue("第一次執行_成功", composite.IsExecuted()));
        
        // 第二次執行（應該被阻止）
        composite.Execute();
        m_runner.RecordResult(Assert::AssertTrue("第二次執行_仍然已執行", composite.IsExecuted()));
        m_runner.RecordResult(Assert::AssertEquals("第二次執行_子流水線調用次數", 1, child.GetExecuteCallCount()));
        
        // 清理
        delete composite;
        delete child;
        
        TearDown();
    }
    
    // 測試GetCount和GetMaxItems方法
    void TestGetCountAndMaxItems()
    {
        SetUp();
        
        PipelineComposite* composite = new PipelineComposite("計數測試", "CountTest", 5);
        
        // 初始狀態
        m_runner.RecordResult(Assert::AssertEquals("初始計數", 0, composite.GetCount()));
        m_runner.RecordResult(Assert::AssertEquals("最大項目數", 5, composite.GetMaxItems()));
        
        // 添加子流水線並檢查計數
        MockPipeline* child1 = new MockPipeline("子流水線1");
        MockPipeline* child2 = new MockPipeline("子流水線2");
        
        composite.Add(child1);
        m_runner.RecordResult(Assert::AssertEquals("添加一個後計數", 1, composite.GetCount()));
        
        composite.Add(child2);
        m_runner.RecordResult(Assert::AssertEquals("添加兩個後計數", 2, composite.GetCount()));
        
        // 移除並檢查計數
        composite.Remove(child1);
        m_runner.RecordResult(Assert::AssertEquals("移除一個後計數", 1, composite.GetCount()));
        
        // 清理
        delete composite;
        delete child1;
        delete child2;
        
        TearDown();
    }
};
