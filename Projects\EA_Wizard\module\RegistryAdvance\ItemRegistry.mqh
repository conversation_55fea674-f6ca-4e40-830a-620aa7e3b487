//+------------------------------------------------------------------+
//|                                                 ItemRegistry.mqh |
//|                                                       EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict


#include "Registry.mqh"

//+------------------------------------------------------------------+
//| ItemRegistry 類 - 用於註冊和管理各種類型的項目                   |
//+------------------------------------------------------------------+
template<typename Val>
class ItemRegistry : public Registry<string, Val>
{
public:
    // 構造函數
    ItemRegistry(const string name = "ItemRegistry", const string type = "ItemRegistry", const int maxItems = 100)
        : Registry<string, Val>(name, type, maxItems)
    {
    }

    // 析構函數
    ~ItemRegistry()
    {
        // 基類析構函數會處理資源釋放
    }

    // 註冊新項目
    RegistryResult<string>* Register(const string name, const string description, Val value) override
    {
        // 檢查是否達到最大項目數量
        if(this.m_items.size() >= this.m_maxItems)
        {
            return new RegistryResult<string>(false, "已達到最大項目數量", "", m_name);
        }

        // 檢查鍵是否已存在
        if(this.m_items.contains(name))
        {
            return new RegistryResult<string>(false, "鍵已存在", "", m_name);
        }

        // 創建新項目
        string idStr = StringFormat("item_%d", GetTickCount());
        RegistryItem<Val>* item = new RegistryItem<Val>(idStr, name, description, value, "Val");

        // 添加到哈希表
        this.m_items.set(name, item);

        // 保存最後註冊的鍵
        this.m_lastRegisteredKey = name;

        return new RegistryResult<string>(true, "註冊成功", name, m_name);
    }
};
