//+------------------------------------------------------------------+
//|                                          TestPipelineResult.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../model/PipelineResult.mqh"

//+------------------------------------------------------------------+
//| PipelineResult測試類別                                           |
//+------------------------------------------------------------------+
class TestPipelineResult : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用
    
public:
    // 構造函數
    TestPipelineResult(TestRunner* runner) 
    : TestCase("TestPipelineResult"), m_runner(runner) {}
    
    // 運行所有測試
    void RunTests() override
    {
        TestConstructor();
        TestIsSuccess();
        TestGetMessage();
        TestGetSource();
        TestSuccessfulResult();
        TestFailedResult();
        TestEmptyValues();
    }
    
private:
    // 測試構造函數
    void TestConstructor()
    {
        SetUp();
        
        // 測試成功結果的構造
        PipelineResult* successResult = new PipelineResult(true, "成功消息", "測試來源");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_成功結果不為空", successResult));
        
        // 測試失敗結果的構造
        PipelineResult* failResult = new PipelineResult(false, "失敗消息", "測試來源");
        m_runner.RecordResult(Assert::AssertNotNull("構造函數_失敗結果不為空", failResult));
        
        // 清理
        delete successResult;
        delete failResult;
        
        TearDown();
    }
    
    // 測試IsSuccess方法
    void TestIsSuccess()
    {
        SetUp();
        
        // 測試成功情況
        PipelineResult* successResult = new PipelineResult(true, "成功", "來源");
        m_runner.RecordResult(Assert::AssertTrue("IsSuccess_成功情況", successResult.IsSuccess()));
        
        // 測試失敗情況
        PipelineResult* failResult = new PipelineResult(false, "失敗", "來源");
        m_runner.RecordResult(Assert::AssertFalse("IsSuccess_失敗情況", failResult.IsSuccess()));
        
        // 清理
        delete successResult;
        delete failResult;
        
        TearDown();
    }
    
    // 測試GetMessage方法
    void TestGetMessage()
    {
        SetUp();
        
        string testMessage = "這是測試消息";
        PipelineResult* result = new PipelineResult(true, testMessage, "來源");
        
        m_runner.RecordResult(Assert::AssertEquals("GetMessage_消息內容", 
            testMessage, result.GetMessage()));
        
        // 測試空消息
        PipelineResult* emptyResult = new PipelineResult(true, "", "來源");
        m_runner.RecordResult(Assert::AssertEquals("GetMessage_空消息", 
            "", emptyResult.GetMessage()));
        
        // 清理
        delete result;
        delete emptyResult;
        
        TearDown();
    }
    
    // 測試GetSource方法
    void TestGetSource()
    {
        SetUp();
        
        string testSource = "測試流水線來源";
        PipelineResult* result = new PipelineResult(true, "消息", testSource);
        
        m_runner.RecordResult(Assert::AssertEquals("GetSource_來源內容", 
            testSource, result.GetSource()));
        
        // 測試空來源
        PipelineResult* emptyResult = new PipelineResult(true, "消息", "");
        m_runner.RecordResult(Assert::AssertEquals("GetSource_空來源", 
            "", emptyResult.GetSource()));
        
        // 清理
        delete result;
        delete emptyResult;
        
        TearDown();
    }
    
    // 測試成功結果的完整場景
    void TestSuccessfulResult()
    {
        SetUp();
        
        PipelineResult* result = new PipelineResult(true, "流水線執行成功", "主流水線");
        
        m_runner.RecordResult(Assert::AssertTrue("成功結果_IsSuccess", result.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("成功結果_消息", 
            "流水線執行成功", result.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("成功結果_來源", 
            "主流水線", result.GetSource()));
        
        delete result;
        
        TearDown();
    }
    
    // 測試失敗結果的完整場景
    void TestFailedResult()
    {
        SetUp();
        
        PipelineResult* result = new PipelineResult(false, "流水線執行失敗", "子流水線");
        
        m_runner.RecordResult(Assert::AssertFalse("失敗結果_IsSuccess", result.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("失敗結果_消息", 
            "流水線執行失敗", result.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("失敗結果_來源", 
            "子流水線", result.GetSource()));
        
        delete result;
        
        TearDown();
    }
    
    // 測試空值和邊界情況
    void TestEmptyValues()
    {
        SetUp();
        
        // 測試所有參數都為空的情況
        PipelineResult* emptyResult = new PipelineResult(false, "", "");
        
        m_runner.RecordResult(Assert::AssertFalse("空值_IsSuccess", emptyResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("空值_消息", "", emptyResult.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("空值_來源", "", emptyResult.GetSource()));
        
        // 測試長字符串
        string longMessage = "這是一個非常長的消息，用來測試PipelineResult是否能正確處理長字符串內容";
        string longSource = "這是一個非常長的來源名稱，用來測試系統的邊界處理能力";
        
        PipelineResult* longResult = new PipelineResult(true, longMessage, longSource);
        
        m_runner.RecordResult(Assert::AssertEquals("長字符串_消息", longMessage, longResult.GetMessage()));
        m_runner.RecordResult(Assert::AssertEquals("長字符串_來源", longSource, longResult.GetSource()));
        
        // 清理
        delete emptyResult;
        delete longResult;
        
        TearDown();
    }
};
