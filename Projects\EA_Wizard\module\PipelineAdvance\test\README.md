# PipelineAdvance 模組測試文檔

## 概述

本目錄包含 PipelineAdvance 模組的完整測試套件，採用模組化設計，提供全面的單元測試和整合測試。

## 目錄結構

```
test/
├── README.md                           # 本文檔
├── TestFramework.mqh                   # 測試框架
├── TestRunner.mqh                      # 測試運行器
├── TestRunnerAdvanced.mqh              # 增強版測試運行器
├── TestDocumentGenerator.mqh           # 測試文檔生成器
├── TestRunnerWithDocuments.mqh         # 支援文檔輸出的測試運行器
├── RunAllTests.mqh                     # 主測試入口
├── DocumentOutputExample.mqh           # 文檔輸出功能使用示例
├── mock/                               # Mock 類別
│   └── MockPipeline.mqh               # Mock 流水線實現
├── unit/                               # 單元測試
│   ├── TestPipelineResult.mqh         # PipelineResult 測試
│   ├── TestPipelineComposite.mqh      # PipelineComposite 測試
│   └── TestPipelineStates.mqh         # 狀態模式測試
└── integration/                        # 整合測試
    └── TestPipelineIntegration.mqh     # 整合場景測試
```

## 快速開始

### 運行所有測試

```mql4
#include "module/PipelineAdvance/test/RunAllTests.mqh"

void OnInit()
{
    // 基本測試（僅控制台輸出）
    RunAllPipelineAdvanceTests();

    // 測試 + 文檔輸出
    RunAllPipelineAdvanceTestsWithDocs();
}
```

### 運行特定類型的測試

```mql4
// 只運行單元測試
RunPipelineAdvanceUnitTests();

// 只運行整合測試
RunPipelineAdvanceIntegrationTests();

// 快速檢查（靜默模式）
bool allPassed = QuickPipelineAdvanceCheck();
```

### 運行特定測試類別

```mql4
// 測試 PipelineResult
RunSpecificPipelineAdvanceTest("TestPipelineResult");

// 測試 PipelineComposite
RunSpecificPipelineAdvanceTest("TestPipelineComposite");

// 測試狀態模式
RunSpecificPipelineAdvanceTest("TestPipelineStates");

// 測試整合場景
RunSpecificPipelineAdvanceTest("TestPipelineIntegration");
```

## 測試框架

### TestFramework.mqh

提供基礎的測試功能：

- **Assert 類別**：提供各種斷言方法
- **TestCase 基類**：所有測試類別的基類
- **TestRunner**：管理和執行測試
- **TestResult**：記錄測試結果

### 斷言方法

```mql4
// 布爾斷言
Assert::AssertTrue("測試名稱", condition, "錯誤消息");
Assert::AssertFalse("測試名稱", condition, "錯誤消息");

// 相等斷言
Assert::AssertEquals("測試名稱", expected, actual, "錯誤消息");

// 指針斷言
Assert::AssertNotNull("測試名稱", pointer, "錯誤消息");
Assert::AssertNull("測試名稱", pointer, "錯誤消息");
```

## 測試類別

### 1. TestPipelineResult

測試 `PipelineResult` 類別的基本功能：

- 構造函數測試
- `IsSuccess()` 方法測試
- `GetMessage()` 和 `GetSource()` 方法測試
- 邊界情況測試

### 2. TestPipelineComposite

測試 `PipelineComposite` 類別的組合模式功能：

- 構造函數和基本屬性測試
- 添加和移除子流水線測試
- 執行邏輯測試
- 容量限制測試
- 重複執行防護測試

### 3. TestPipelineStates

測試狀態模式的實現：

- 各種狀態類別的基本功能
- 狀態轉換邏輯
- 狀態描述測試

### 4. TestPipelineIntegration

測試整合場景：

- 簡單工作流程
- 複雜嵌套結構
- 錯誤處理
- 大規模場景
- 邊界情況

## Mock 類別

### MockPipeline

提供用於測試的模擬流水線：

```mql4
// 創建成功的 Mock 流水線
MockPipeline* success = MockPipelineFactory::CreateSuccessfulPipeline("測試");

// 創建失敗的 Mock 流水線
MockPipeline* failed = MockPipelineFactory::CreateFailedPipeline("測試");

// 檢查調用次數
int callCount = mockPipeline.GetExecuteCallCount();
```

## 測試最佳實踐

### 1. 測試命名

- 使用描述性的測試名稱
- 包含測試的功能和預期結果
- 使用中文註解說明測試目的

### 2. 測試結構

```mql4
void TestSpecificFeature()
{
    SetUp();        // 準備測試環境

    // 執行測試邏輯
    // 使用斷言驗證結果

    TearDown();     // 清理測試環境
}
```

### 3. 資源管理

- 在測試中創建的對象必須在測試結束時清理
- 使用 `SetUp()` 和 `TearDown()` 方法管理資源
- 避免記憶體洩漏

### 4. 測試獨立性

- 每個測試應該獨立運行
- 不依賴其他測試的執行順序
- 不共享狀態

## 持續整合

### CI 測試

```mql4
void OnInit()
{
    CIPipelineAdvanceTests();
}
```

### 回歸測試

```mql4
void OnInit()
{
    RunRegressionTests();
}
```

### 性能測試

```mql4
void OnInit()
{
    RunPerformanceTests();
}
```

## 故障排除

### 常見問題

1. **編譯錯誤**

   - 檢查所有依賴檔案是否正確包含
   - 確認檔案路徑正確

2. **測試失敗**

   - 查看詳細的錯誤消息
   - 檢查被測試的代碼是否有變更

3. **記憶體問題**
   - 確保所有 `new` 操作都有對應的 `delete`
   - 檢查指針是否正確初始化

### 調試技巧

1. **使用詳細模式**

   ```mql4
   PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner(true, true, true);
   ```

2. **運行單個測試**

   ```mql4
   RunSpecificPipelineAdvanceTest("TestPipelineResult");
   ```

3. **檢查測試環境**
   ```mql4
   ValidateTestEnvironment();
   ```

## 擴展測試

### 添加新測試

1. 在適當的目錄創建新的測試檔案
2. 繼承 `TestCase` 基類
3. 實現 `RunTests()` 方法
4. 在 `TestRunner.mqh` 中添加新測試

### 添加新斷言

在 `TestFramework.mqh` 的 `Assert` 類別中添加新方法。

## 文檔輸出功能

### 概述

PipelineAdvance 測試模組現在支援將測試結果輸出為 txt 文檔，提供詳細的測試報告和摘要。

### 文檔輸出功能

#### 基本使用

```mql4
// 運行所有測試並生成文檔
RunAllPipelineAdvanceTestsWithDocs();

// 運行單元測試並生成文檔
RunPipelineAdvanceUnitTestsWithDocs();

// 運行整合測試並生成文檔
RunPipelineAdvanceIntegrationTestsWithDocs();

// 運行特定測試並生成文檔
RunSpecificPipelineAdvanceTestWithDocs("TestPipelineResult");
```

#### 自定義文檔選項

```mql4
// 基本自定義文檔輸出選項
RunPipelineAdvanceTestsWithCustomDocs(
    true,                           // 生成完整報告
    true,                           // 生成摘要
    "MyCustomTestReports"           // 自定義輸出目錄
);

// 高級自定義文檔輸出選項（包含顯示數量設置）
RunPipelineAdvanceTestsWithAdvancedDocs(
    true,                           // 生成完整報告
    true,                           // 生成摘要
    "MyAdvancedReports",            // 自定義輸出目錄
    15                              // 最多顯示15個通過的測試
);
```

#### 通過測試顯示選項

```mql4
// 限制顯示通過測試數量
RunPipelineAdvanceTestsWithLimitedDisplay(5);          // 只顯示前5個
RunPipelineAdvanceTestsWithLimitedDisplay(0);          // 不顯示通過的測試

// 無限制顯示所有通過的測試
RunPipelineAdvanceTestsWithUnlimitedDisplay();

// 程式化設置顯示選項
PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
runner.SetPassedTestsDisplayOptions(20);               // 顯示前20個
runner.SetUnlimitedPassedTestsDisplay();               // 無限制顯示
runner.RunAllTestsWithDocs();
delete runner;
```

#### 完整測試套件

```mql4
// 運行完整測試套件並生成文檔
FullPipelineAdvanceTestSuiteWithDocs();
```

### 生成的文檔

#### 完整報告 (PipelineAdvance_TestReport_YYYYMMDD_HHMM.txt)

- **測試執行摘要**: 總測試數、通過數、失敗數、成功率
- **詳細統計信息**: 通過率、失敗率等
- **執行時間統計**: 總時間、平均時間、最長/最短執行時間
- **失敗測試詳情**: 包含具體錯誤信息和測試類別（始終完整顯示）
- **通過測試列表**: 根據顯示設置顯示通過的測試及執行時間
- **按類別分組結果**: 每個測試類別的詳細統計

#### 通過測試顯示選項說明

- **默認設置**: 顯示前 10 個通過的測試
- **限制顯示**:
  - 正數 (如 5, 15, 20): 顯示指定數量的通過測試
  - 0: 不顯示通過的測試（只顯示失敗的測試）
- **無限制顯示**: -1 或使用 `SetUnlimitedPassedTestsDisplay()` 顯示所有通過的測試
- **智能提示**: 當有更多測試時，會顯示省略信息和設置提示

#### 測試摘要 (PipelineAdvance_TestReport_Summary_YYYYMMDD_HHMM.txt)

- **簡化摘要**: 基本測試統計信息
- **快速查看**: 適合快速了解測試結果
- **輕量級**: 文件較小，便於分享

### 文檔保存位置

- **默認位置**: `MQL4\Files\TestReports\`
- **自定義位置**: `MQL4\Files\[自定義目錄名]\`
- **文件命名**: 包含時間戳，避免文件覆蓋

### 使用示例

詳細的使用示例請參考以下文件：

- `DocumentOutputExample.mqh`: 基本文檔輸出功能示例
- `TestDisplayOptionsDemo.mq4`: 通過測試顯示選項演示
- `TestDocumentOutputFixed.mq4`: 修復驗證和基本功能測試

包含的示例：

- 基本文檔輸出示例
- 分類測試文檔輸出
- 特定測試類別文檔輸出
- 自定義文檔選項示例
- 通過測試顯示選項配置
- 故障排除指南

### 最佳實踐建議

#### 顯示數量選擇指南

- **開發階段**: 使用無限制顯示 (`-1`)，查看所有測試詳情
- **日常測試**: 使用默認設置 (10 個) 或適中限制 (5-15 個)
- **生產環境**: 使用較小限制 (3-5 個)，保持文檔簡潔
- **調試失敗**: 使用限制 0，只關注失敗的測試
- **報告展示**: 使用適中限制 (5-10 個)，平衡詳細度和可讀性

#### 性能考慮

- **文檔大小**: 無限制顯示會產生較大的文檔文件
- **生成速度**: 限制顯示可以提高文檔生成速度
- **記憶體使用**: 測試結果收集不受顯示限制影響

### 故障排除

如果文檔生成失敗，請檢查：

1. **文件權限**: 確保 `MQL4\Files\` 目錄可寫
2. **磁盤空間**: 確保有足夠的磁盤空間
3. **防毒軟件**: 檢查是否阻止文件創建
4. **目錄結構**: MQL4 會自動創建必要的子目錄

## 版本歷史

- **v1.2** - 添加可配置的通過測試顯示選項

  - 新增可配置的通過測試顯示數量設置
  - 支援限制顯示、無限制顯示和不顯示選項
  - 添加智能提示和總計信息顯示
  - 新增高級自定義文檔輸出函數
  - 提供通過測試顯示選項演示腳本
  - 添加最佳實踐建議和性能考慮指南

- **v1.1.1** - 修復指針訪問錯誤

  - 修復 TestRunnerAdvanced 中的指針訪問錯誤
  - 改進 TestDocumentGenerator 的類型處理
  - 移除不支援的 dynamic_cast 使用
  - 添加專門的 GenerateAdvancedTestReport 方法
  - 提供修復驗證測試腳本

- **v1.1** - 添加文檔輸出功能

  - 新增 TestDocumentGenerator 類別
  - 新增 TestRunnerAdvanced 增強版測試運行器
  - 新增 TestRunnerWithDocuments 支援文檔輸出
  - 支援生成完整報告和測試摘要
  - 支援自定義輸出目錄和文檔選項
  - 提供詳細的使用示例和故障排除指南

- **v1.0** - 初始版本，包含基本的單元測試和整合測試
  - 支援 PipelineResult、PipelineComposite 和狀態模式測試
  - 提供完整的測試框架和 Mock 類別

## 貢獻指南

1. 遵循現有的代碼風格
2. 為新功能添加相應的測試
3. 確保所有測試通過
4. 更新相關文檔
