//+------------------------------------------------------------------+
//|                                   TestEAPipelineIntegration.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../components/EAPipelineManager.mqh"
#include "../../components/EACompoundPipeline.mqh"
#include "../mock/MockEAPipeline.mqh"

//+------------------------------------------------------------------+
//| EA流水線整合測試類別                                             |
//+------------------------------------------------------------------+
class TestEAPipelineIntegration : public TestCase
{
private:
    TestRunner* m_runner;    // 測試運行器引用

public:
    // 構造函數
    TestEAPipelineIntegration(TestRunner* runner)
    : TestCase("TestEAPipelineIntegration"), m_runner(runner) {}

    // 運行所有測試
    void RunTests() override
    {
        TestSimpleWorkflow();
        TestComplexWorkflow();
        TestErrorHandling();
        TestMixedResults();
        TestNestedPipelines();
        TestLargeScale();
        TestOnInitWorkflow();
        TestOnTickWorkflow();
        TestOnDeinitWorkflow();
        TestManagerWithCompoundPipelines();
    }

private:
    // 測試簡單工作流程
    void TestSimpleWorkflow()
    {
        SetUp();

        // 創建管理器和複合流水線
        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        EACompoundPipeline* compound = new EACompoundPipeline("SimpleCompound");

        // 創建子流水線
        MockEAPipeline* child1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child1");
        MockEAPipeline* child2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Child2");

        // 構建工作流程
        compound.Add(child1);
        compound.Add(child2);
        manager.AddPipeline(compound);

        // 執行工作流程
        PipelineResult* result = manager.ExecutePipeline("SimpleCompound");

        // 驗證結果
        m_runner.RecordResult(Assert::AssertNotNull("簡單工作流程_結果不為空", result));

        if(result != NULL)
        {
            m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_執行成功", result.IsSuccess()));
            delete result;
        }

        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_複合流水線已執行", compound.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_子流水線1已執行", child1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("簡單工作流程_子流水線2已執行", child2.IsExecuted()));

        // 清理測試數據
        manager.RemovePipelineByName("SimpleCompound");
        TearDown();
    }

    // 測試複雜工作流程
    void TestComplexWorkflow()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        // 創建多個複合流水線
        EACompoundPipeline* compound1 = new EACompoundPipeline("Compound1");
        EACompoundPipeline* compound2 = new EACompoundPipeline("Compound2");

        // 為每個複合流水線添加子流水線
        for(int i = 0; i < 3; i++)
        {
            MockEAPipeline* child1 = MockEAPipelineFactory::CreateSuccessfulPipeline(StringFormat("C1_Child%d", i));
            MockEAPipeline* child2 = MockEAPipelineFactory::CreateSuccessfulPipeline(StringFormat("C2_Child%d", i));

            compound1.Add(child1);
            compound2.Add(child2);
        }

        // 添加到管理器
        manager.AddPipeline(compound1);
        manager.AddPipeline(compound2);

        // 執行所有流水線
        PipelineResult* result1 = manager.ExecutePipeline("Compound1");
        PipelineResult* result2 = manager.ExecutePipeline("Compound2");

        // 驗證結果
        m_runner.RecordResult(Assert::AssertTrue("複雜工作流程_複合流水線1成功", result1.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("複雜工作流程_複合流水線2成功", result2.IsSuccess()));
        m_runner.RecordResult(Assert::AssertEquals("複雜工作流程_複合流水線1子數量", 3, compound1.GetCount()));
        m_runner.RecordResult(Assert::AssertEquals("複雜工作流程_複合流水線2子數量", 3, compound2.GetCount()));

        delete result1;
        delete result2;

        // 清理測試數據
        manager.RemovePipelineByName("Compound1");
        manager.RemovePipelineByName("Compound2");
        TearDown();
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();
        EACompoundPipeline* compound = new EACompoundPipeline("ErrorCompound");

        // 創建混合的成功和失敗流水線
        MockEAPipeline* success = MockEAPipelineFactory::CreateSuccessfulPipeline("Success");
        MockEAPipeline* failed = MockEAPipelineFactory::CreateFailedPipeline("Failed");

        compound.Add(success);
        compound.Add(failed);
        manager.AddPipeline(compound);

        // 執行包含錯誤的工作流程
        PipelineResult* result = manager.ExecutePipeline("ErrorCompound");

        // 驗證錯誤處理
        m_runner.RecordResult(Assert::AssertNotNull("錯誤處理_結果不為空", result));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_成功流水線已執行", success.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線已執行", failed.IsExecuted()));

        // 檢查失敗流水線的結果
        PipelineResult* failedResult = failed.GetResult();
        if(failedResult != NULL)
        {
            m_runner.RecordResult(Assert::AssertFalse("錯誤處理_失敗流水線結果為失敗", failedResult.IsSuccess()));
        }

        delete result;

        // 清理測試數據
        manager.RemovePipelineByName("ErrorCompound");
        TearDown();
    }

    // 測試混合結果
    void TestMixedResults()
    {
        SetUp();

        EACompoundPipeline* compound = new EACompoundPipeline("MixedCompound");

        // 創建不同類型的流水線
        MockEAPipeline* success1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Success1");
        MockEAPipeline* success2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Success2");
        MockEAPipeline* failed1 = MockEAPipelineFactory::CreateFailedPipeline("Failed1");

        compound.Add(success1);
        compound.Add(failed1);
        compound.Add(success2);

        // 執行混合流水線
        compound.Execute();

        // 驗證所有流水線都被執行
        m_runner.RecordResult(Assert::AssertTrue("混合結果_成功流水線1已執行", success1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("混合結果_失敗流水線已執行", failed1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("混合結果_成功流水線2已執行", success2.IsExecuted()));

        // 驗證結果狀態
        m_runner.RecordResult(Assert::AssertTrue("混合結果_成功流水線1結果", success1.GetResult().IsSuccess()));
        m_runner.RecordResult(Assert::AssertFalse("混合結果_失敗流水線結果", failed1.GetResult().IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("混合結果_成功流水線2結果", success2.GetResult().IsSuccess()));

        delete compound;
        TearDown();
    }

    // 測試嵌套流水線
    void TestNestedPipelines()
    {
        SetUp();

        // 創建嵌套結構
        EACompoundPipeline* parent = new EACompoundPipeline("Parent");
        EACompoundPipeline* child1 = new EACompoundPipeline("Child1");
        EACompoundPipeline* child2 = new EACompoundPipeline("Child2");

        // 為子複合流水線添加葉子節點
        MockEAPipeline* leaf1 = MockEAPipelineFactory::CreateSuccessfulPipeline("Leaf1");
        MockEAPipeline* leaf2 = MockEAPipelineFactory::CreateSuccessfulPipeline("Leaf2");
        MockEAPipeline* leaf3 = MockEAPipelineFactory::CreateSuccessfulPipeline("Leaf3");

        child1.Add(leaf1);
        child1.Add(leaf2);
        child2.Add(leaf3);

        parent.Add(child1);
        parent.Add(child2);

        // 執行嵌套結構
        parent.Execute();

        // 驗證嵌套執行
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_父流水線已執行", parent.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_子流水線1已執行", child1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_子流水線2已執行", child2.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_葉子節點1已執行", leaf1.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_葉子節點2已執行", leaf2.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("嵌套流水線_葉子節點3已執行", leaf3.IsExecuted()));

        delete parent;
        TearDown();
    }

    // 測試大規模場景
    void TestLargeScale()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        // 創建大量流水線
        const int PIPELINE_COUNT = 20;
        const int CHILDREN_PER_PIPELINE = 5;

        for(int i = 0; i < PIPELINE_COUNT; i++)
        {
            EACompoundPipeline* compound = new EACompoundPipeline(StringFormat("Compound%d", i));

            for(int j = 0; j < CHILDREN_PER_PIPELINE; j++)
            {
                MockEAPipeline* child = MockEAPipelineFactory::CreateSuccessfulPipeline(
                    StringFormat("Child%d_%d", i, j));
                compound.Add(child);
            }

            manager.AddPipeline(compound);
        }

        // 驗證大規模創建
        m_runner.RecordResult(Assert::AssertEquals("大規模場景_流水線數量", PIPELINE_COUNT, manager.GetPipelineCount()));

        // 執行部分流水線
        for(int i = 0; i < 5; i++)
        {
            string pipelineName = StringFormat("Compound%d", i);
            PipelineResult* result = manager.ExecutePipeline(pipelineName);

            if(result != NULL)
            {
                m_runner.RecordResult(Assert::AssertTrue(
                    StringFormat("大規模場景_流水線%d執行成功", i), result.IsSuccess()));
                delete result;
            }
        }

        // 清理測試數據
        for(int i = 0; i < 5; i++)
        {
            string pipelineName = StringFormat("Compound%d", i);
            manager.RemovePipelineByName(pipelineName);
        }
        TearDown();
    }

    // 測試OnInit工作流程
    void TestOnInitWorkflow()
    {
        SetUp();

        EACompoundPipeline* onInitPipeline = new EACompoundPipeline("OnInit");

        // 模擬OnInit階段的流水線
        MockEAPipeline* paramRead = MockEAPipelineFactory::CreateOnInitPipeline("ParameterRead");
        MockEAPipeline* varInit = MockEAPipelineFactory::CreateOnInitPipeline("VariableInit");
        MockEAPipeline* envCheck = MockEAPipelineFactory::CreateOnInitPipeline("EnvironmentCheck");

        onInitPipeline.Add(paramRead);
        onInitPipeline.Add(varInit);
        onInitPipeline.Add(envCheck);

        // 執行OnInit工作流程
        onInitPipeline.Execute();

        // 驗證OnInit流程
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_主流水線已執行", onInitPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_參數讀取已執行", paramRead.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_變數初始化已執行", varInit.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnInit工作流程_環境檢查已執行", envCheck.IsExecuted()));

        delete onInitPipeline;
        TearDown();
    }

    // 測試OnTick工作流程
    void TestOnTickWorkflow()
    {
        SetUp();

        EACompoundPipeline* onTickPipeline = new EACompoundPipeline("OnTick");

        // 模擬OnTick階段的流水線
        MockEAPipeline* dataFeed = MockEAPipelineFactory::CreateOnTickPipeline("DataFeed");
        MockEAPipeline* signal = MockEAPipelineFactory::CreateOnTickPipeline("Signal");
        MockEAPipeline* order = MockEAPipelineFactory::CreateOnTickPipeline("Order");
        MockEAPipeline* risk = MockEAPipelineFactory::CreateOnTickPipeline("Risk");

        onTickPipeline.Add(dataFeed);
        onTickPipeline.Add(signal);
        onTickPipeline.Add(order);
        onTickPipeline.Add(risk);

        // 執行OnTick工作流程
        onTickPipeline.Execute();

        // 驗證OnTick流程
        m_runner.RecordResult(Assert::AssertTrue("OnTick工作流程_主流水線已執行", onTickPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnTick工作流程_數據饋送已執行", dataFeed.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnTick工作流程_信號處理已執行", signal.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnTick工作流程_訂單處理已執行", order.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnTick工作流程_風險管理已執行", risk.IsExecuted()));

        delete onTickPipeline;
        TearDown();
    }

    // 測試OnDeinit工作流程
    void TestOnDeinitWorkflow()
    {
        SetUp();

        EACompoundPipeline* onDeinitPipeline = new EACompoundPipeline("OnDeinit");

        // 模擬OnDeinit階段的流水線
        MockEAPipeline* cleanup = MockEAPipelineFactory::CreateOnDeinitPipeline("Cleanup");
        MockEAPipeline* saveState = MockEAPipelineFactory::CreateOnDeinitPipeline("SaveState");
        MockEAPipeline* logSummary = MockEAPipelineFactory::CreateOnDeinitPipeline("LogSummary");

        onDeinitPipeline.Add(cleanup);
        onDeinitPipeline.Add(saveState);
        onDeinitPipeline.Add(logSummary);

        // 執行OnDeinit工作流程
        onDeinitPipeline.Execute();

        // 驗證OnDeinit流程
        m_runner.RecordResult(Assert::AssertTrue("OnDeinit工作流程_主流水線已執行", onDeinitPipeline.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnDeinit工作流程_清理已執行", cleanup.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnDeinit工作流程_狀態保存已執行", saveState.IsExecuted()));
        m_runner.RecordResult(Assert::AssertTrue("OnDeinit工作流程_日誌摘要已執行", logSummary.IsExecuted()));

        delete onDeinitPipeline;
        TearDown();
    }

    // 測試管理器與複合流水線的整合
    void TestManagerWithCompoundPipelines()
    {
        SetUp();

        EAPipelineManager* manager = EAPipelineManager::GetInstance();

        // 創建多個複合流水線代表不同階段
        EACompoundPipeline* onInit = new EACompoundPipeline("OnInit");
        EACompoundPipeline* onTick = new EACompoundPipeline("OnTick");
        EACompoundPipeline* onDeinit = new EACompoundPipeline("OnDeinit");

        // 為每個階段添加子流水線
        onInit.Add(MockEAPipelineFactory::CreateOnInitPipeline("InitStep1"));
        onInit.Add(MockEAPipelineFactory::CreateOnInitPipeline("InitStep2"));

        onTick.Add(MockEAPipelineFactory::CreateOnTickPipeline("TickStep1"));
        onTick.Add(MockEAPipelineFactory::CreateOnTickPipeline("TickStep2"));
        onTick.Add(MockEAPipelineFactory::CreateOnTickPipeline("TickStep3"));

        onDeinit.Add(MockEAPipelineFactory::CreateOnDeinitPipeline("DeinitStep1"));

        // 添加到管理器
        manager.AddPipeline(onInit);
        manager.AddPipeline(onTick);
        manager.AddPipeline(onDeinit);

        // 模擬完整的EA生命週期
        PipelineResult* initResult = manager.ExecutePipeline("OnInit");
        PipelineResult* tickResult = manager.ExecutePipeline("OnTick");
        PipelineResult* deinitResult = manager.ExecutePipeline("OnDeinit");

        // 驗證完整生命週期
        m_runner.RecordResult(Assert::AssertTrue("管理器整合_OnInit成功", initResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("管理器整合_OnTick成功", tickResult.IsSuccess()));
        m_runner.RecordResult(Assert::AssertTrue("管理器整合_OnDeinit成功", deinitResult.IsSuccess()));

        m_runner.RecordResult(Assert::AssertEquals("管理器整合_OnInit子數量", 2, onInit.GetCount()));
        m_runner.RecordResult(Assert::AssertEquals("管理器整合_OnTick子數量", 3, onTick.GetCount()));
        m_runner.RecordResult(Assert::AssertEquals("管理器整合_OnDeinit子數量", 1, onDeinit.GetCount()));

        delete initResult;
        delete tickResult;
        delete deinitResult;

        // 清理測試數據
        manager.RemovePipeline("OnInit");
        manager.RemovePipeline("OnTick");
        manager.RemovePipeline("OnDeinit");
        TearDown();
    }
};
