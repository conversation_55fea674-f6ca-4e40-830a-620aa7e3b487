//+------------------------------------------------------------------+
//|                                                  TestRunner.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestFramework.mqh"
#include "unit/TestPipelineResult.mqh"
#include "unit/TestPipelineComposite.mqh"
#include "unit/TestPipelineStates.mqh"
#include "integration/TestPipelineIntegration.mqh"

//+------------------------------------------------------------------+
//| PipelineAdvance模組測試運行器                                    |
//+------------------------------------------------------------------+
class PipelineAdvanceTestRunner
{
private:
    TestRunner* m_runner;           // 測試框架運行器
    bool m_runUnitTests;            // 是否運行單元測試
    bool m_runIntegrationTests;     // 是否運行整合測試
    bool m_verbose;                 // 是否詳細輸出
    
public:
    // 構造函數
    PipelineAdvanceTestRunner(bool runUnitTests = true, bool runIntegrationTests = true, bool verbose = true)
    : m_runUnitTests(runUnitTests), m_runIntegrationTests(runIntegrationTests), m_verbose(verbose)
    {
        m_runner = new TestRunner();
    }
    
    // 析構函數
    ~PipelineAdvanceTestRunner()
    {
        if(m_runner != NULL)
        {
            delete m_runner;
            m_runner = NULL;
        }
    }
    
    // 運行所有測試
    void RunAllTests()
    {
        PrintHeader();
        
        if(m_runUnitTests)
        {
            RunUnitTests();
        }
        
        if(m_runIntegrationTests)
        {
            RunIntegrationTests();
        }
        
        PrintSummary();
    }
    
    // 只運行單元測試
    void RunUnitTestsOnly()
    {
        PrintHeader();
        RunUnitTests();
        PrintSummary();
    }
    
    // 只運行整合測試
    void RunIntegrationTestsOnly()
    {
        PrintHeader();
        RunIntegrationTests();
        PrintSummary();
    }
    
    // 運行特定測試類別
    void RunSpecificTest(string testClassName)
    {
        PrintHeader();
        
        if(testClassName == "TestPipelineResult")
        {
            TestPipelineResult* test = new TestPipelineResult(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineComposite")
        {
            TestPipelineComposite* test = new TestPipelineComposite(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineStates")
        {
            TestPipelineStates* test = new TestPipelineStates(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else if(testClassName == "TestPipelineIntegration")
        {
            TestPipelineIntegration* test = new TestPipelineIntegration(m_runner);
            m_runner.RunTestCase(test);
            delete test;
        }
        else
        {
            Print("錯誤: 未知的測試類別 - " + testClassName);
            Print("可用的測試類別:");
            Print("  - TestPipelineResult");
            Print("  - TestPipelineComposite");
            Print("  - TestPipelineStates");
            Print("  - TestPipelineIntegration");
        }
        
        PrintSummary();
    }
    
    // 獲取測試結果
    bool AllTestsPassed() const
    {
        return m_runner.AllTestsPassed();
    }
    
    // 獲取測試統計
    int GetTotalTests() const { return m_runner.GetTotalTests(); }
    int GetPassedTests() const { return m_runner.GetPassedTests(); }
    int GetFailedTests() const { return m_runner.GetFailedTests(); }
    
private:
    // 運行單元測試
    void RunUnitTests()
    {
        if(m_verbose)
        {
            Print("🧪 開始運行單元測試...");
            Print("=====================================");
        }
        
        // 運行PipelineResult測試
        TestPipelineResult* resultTest = new TestPipelineResult(m_runner);
        m_runner.RunTestCase(resultTest);
        delete resultTest;
        
        // 運行PipelineComposite測試
        TestPipelineComposite* compositeTest = new TestPipelineComposite(m_runner);
        m_runner.RunTestCase(compositeTest);
        delete compositeTest;
        
        // 運行PipelineStates測試
        TestPipelineStates* statesTest = new TestPipelineStates(m_runner);
        m_runner.RunTestCase(statesTest);
        delete statesTest;
        
        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 單元測試完成");
            Print("");
        }
    }
    
    // 運行整合測試
    void RunIntegrationTests()
    {
        if(m_verbose)
        {
            Print("🔗 開始運行整合測試...");
            Print("=====================================");
        }
        
        // 運行整合測試
        TestPipelineIntegration* integrationTest = new TestPipelineIntegration(m_runner);
        m_runner.RunTestCase(integrationTest);
        delete integrationTest;
        
        if(m_verbose)
        {
            Print("=====================================");
            Print("✅ 整合測試完成");
            Print("");
        }
    }
    
    // 打印測試頭部信息
    void PrintHeader()
    {
        if(m_verbose)
        {
            Print("╔══════════════════════════════════════════════════════════════╗");
            Print("║                PipelineAdvance 模組測試套件                  ║");
            Print("║                        EA_Wizard                             ║");
            Print("╚══════════════════════════════════════════════════════════════╝");
            Print("");
            Print("📋 測試配置:");
            Print("   • 單元測試: " + (m_runUnitTests ? "啟用" : "禁用"));
            Print("   • 整合測試: " + (m_runIntegrationTests ? "啟用" : "禁用"));
            Print("   • 詳細輸出: " + (m_verbose ? "啟用" : "禁用"));
            Print("");
        }
    }
    
    // 打印測試摘要
    void PrintSummary()
    {
        if(m_verbose)
        {
            Print("");
            Print("╔══════════════════════════════════════════════════════════════╗");
            Print("║                        測試執行摘要                          ║");
            Print("╚══════════════════════════════════════════════════════════════╝");
        }
        
        m_runner.PrintSummary();
        
        if(m_verbose)
        {
            Print("");
            if(AllTestsPassed())
            {
                Print("🎉 恭喜！所有測試都通過了！");
                Print("   PipelineAdvance模組運行正常，可以安全使用。");
            }
            else
            {
                Print("❌ 有測試失敗！");
                Print("   請檢查上述錯誤信息並修復相關問題。");
                Print("   建議在修復後重新運行測試。");
            }
            Print("");
            Print("📊 測試覆蓋範圍:");
            Print("   • PipelineResult: 基本功能測試");
            Print("   • PipelineComposite: 組合模式測試");
            Print("   • PipelineStates: 狀態模式測試");
            Print("   • Integration: 整合場景測試");
            Print("");
            Print("🔧 如需重新運行特定測試，請使用:");
            Print("   RunSpecificTest(\"測試類別名稱\")");
        }
    }
};

//+------------------------------------------------------------------+
//| 快速測試函數 - 提供便捷的測試入口                                 |
//+------------------------------------------------------------------+

// 運行所有測試
void RunAllPipelineAdvanceTests()
{
    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner();
    runner.RunAllTests();
    delete runner;
}

// 運行單元測試
void RunPipelineAdvanceUnitTests()
{
    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner(true, false);
    runner.RunUnitTestsOnly();
    delete runner;
}

// 運行整合測試
void RunPipelineAdvanceIntegrationTests()
{
    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner(false, true);
    runner.RunIntegrationTestsOnly();
    delete runner;
}

// 運行特定測試
void RunSpecificPipelineAdvanceTest(string testClassName)
{
    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner();
    runner.RunSpecificTest(testClassName);
    delete runner;
}

// 快速檢查（靜默模式）
bool QuickPipelineAdvanceCheck()
{
    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner(true, true, false);
    runner.RunAllTests();
    bool result = runner.AllTestsPassed();
    delete runner;
    return result;
}
