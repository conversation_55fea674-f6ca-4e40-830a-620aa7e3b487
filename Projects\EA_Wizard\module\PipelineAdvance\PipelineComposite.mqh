//+------------------------------------------------------------------+
//|                                            PipelineComposite.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "interface/IPipeline.mqh"
#include "model/PipelineResult.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 流水線組合類 - 實現組合模式的流水線                              |
//+------------------------------------------------------------------+
class PipelineComposite : public IPipeline
{
private:
    string m_name;                // 流水線名稱
    string m_type;                // 流水線類型
    Vector<IPipeline*> m_children;  // 子流水線向量
    PipelineResult* m_result;     // 流水線執行結果
    bool m_executed;              // 是否已執行
    int m_max_children;           // 最大子流水線數量

public:
    // 構造函數
    PipelineComposite(string name, string type = "PipelineComposite", int maxItems = 100)
    : m_name(name), m_type(type), m_executed(false), m_children(false), m_max_children(maxItems) // 初始化 Vector 並設置為擁有元素
    {
        // 初始化結果為默認值
        m_result = new PipelineResult(false, "尚未執行", m_name);
    }

    // 析構函數
    ~PipelineComposite()
    {
        // 清理結果
        if(m_result != NULL)
        {
            m_result = NULL;
        }
        // Vector 的析構函數會自動清理子流水線
    }

    // 添加子流水線
    PipelineResult* Add(IPipeline* child)
    {
        if(child == NULL)
        {
            return new PipelineResult(false, "子流水線為空", m_name);
        }

        // 檢查是否超過最大子流水線數量限制
        if(m_children.size() >= m_max_children)
        {
            return new PipelineResult(false, "超過最大子流水線數量限制: " + IntegerToString(m_max_children), m_name);
        }

        bool added = m_children.add(child);
        if(added)
        {
            return new PipelineResult(true, "成功添加子流水線: " + child.GetName(), m_name);
        }
        else
        {
            return new PipelineResult(false, "添加子流水線失敗: " + child.GetName(), m_name);
        }
    }

    // 移除子流水線
    PipelineResult* Remove(IPipeline* child)
    {
        if(child == NULL)
        {
            return new PipelineResult(false, "子流水線為空", m_name);
        }

        bool removed = m_children.remove(child);
        if(removed)
        {
            return new PipelineResult(true, "成功移除子流水線: " + child.GetName(), m_name);
        }
        else
        {
            return new PipelineResult(false, "移除子流水線失敗: " + child.GetName(), m_name);
        }
    }

    // 執行流水線
    void Execute() override
    {
        // 檢查是否已執行
        if(m_executed)
        {
            // 如果已執行，直接返回，不重複執行
            return;
        }

        int size = m_children.size();
        bool success = true;
        string message = "所有子流水線執行成功";

        // 如果沒有子流水線，設置結果為成功
        if(size == 0)
        {
            SetResult(true, "沒有子流水線需要執行");
            return;
        }

        // 先檢查所有子流水線是否都未執行
        for(int i = 0; i < size; i++)
        {
            IPipeline* child = m_children.get(i);
            if(child != NULL && child.IsExecuted())
            {
                // 如果發現任何一個子流水線已經執行過，設置失敗結果並返回
                SetResult(false, "子流水線 '" + child.GetName() + "' 已經執行過，無法重複執行");
                return;
            }
        }

        // 執行所有子流水線
        for(int i = 0; i < size; i++)
        {
            IPipeline* child = m_children.get(i);
            if(child != NULL)
            {
                child.Execute();

                // 檢查子流水線執行結果
                PipelineResult* childResult = child.GetResult();
                if(childResult != NULL && !childResult.IsSuccess())
                {
                    success = false;
                    message = childResult.GetMessage();
                    break;
                }
            }
        }

        // 設置結果
        SetResult(success, message);
    }

    // 獲取流水線名稱
    string GetName() override { return m_name; }

    // 獲取流水線類型
    string GetType() override { return m_type; }

    // 獲取流水線執行結果
    PipelineResult* GetResult() override { return m_result; }

    // 重置流水線狀態
    void Restore() override
    {
        // 重置結果
        if(m_result != NULL)
        {
            m_result = NULL;
        }
        // 初始化結果為默認值
        m_result = new PipelineResult(false, "尚未執行", m_name);

        // 重置所有子流水線
        int size = m_children.size();
        for(int i = 0; i < size; i++)
        {
            IPipeline* child = m_children.get(i);
            if(child != NULL)
            {
                child.Restore();
            }
        }

        m_executed = false;
    }

    // 檢查流水線是否已執行
    bool IsExecuted() override { return m_executed; }

    // 獲取最大子流水線數量
    int GetMaxItems() { return m_max_children; }

    int GetCount() { return m_children.size(); }

    int GetAllItems(IPipeline* &items[])
    {
        int size = m_children.size();
        ArrayResize(items, size);

        for(int i = 0; i < size; i++)
        {
            items[i] = m_children.get(i);
        }

        return size;
    }

private:
    // 設置流水線執行結果
    void SetResult(bool success, string message, string source = "")
    {
        if(m_result != NULL)
        {
            m_result = NULL;
        }

        if(source == "")
        {
            source = m_name;
        }

        m_result = new PipelineResult(success, message, source);
        m_executed = true;
    }
};