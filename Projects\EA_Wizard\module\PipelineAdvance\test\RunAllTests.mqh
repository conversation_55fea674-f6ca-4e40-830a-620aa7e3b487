//+------------------------------------------------------------------+
//|                                               RunAllTests.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "TestRunner.mqh"
#include "TestRunnerWithDocuments.mqh"

//+------------------------------------------------------------------+
//| PipelineAdvance模組測試主入口                                    |
//| 這個檔案提供了運行PipelineAdvance模組所有測試的主要入口點         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 主測試函數 - 運行所有測試                                         |
//+------------------------------------------------------------------+
void RunPipelineAdvanceTests()
{
    Print("開始執行PipelineAdvance模組測試...");
    Print("測試時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
    Print("");

    // 運行所有測試
    RunAllPipelineAdvanceTests();

    Print("");
    Print("PipelineAdvance模組測試執行完成。");
}

//+------------------------------------------------------------------+
//| 測試菜單函數 - 提供交互式測試選項                                 |
//+------------------------------------------------------------------+
void ShowPipelineAdvanceTestMenu()
{
    Print("╔══════════════════════════════════════════════════════════════╗");
    Print("║              PipelineAdvance 測試菜單                        ║");
    Print("╚══════════════════════════════════════════════════════════════╝");
    Print("");
    Print("可用的測試選項:");
    Print("1. RunAllPipelineAdvanceTests()           - 運行所有測試");
    Print("2. RunPipelineAdvanceUnitTests()          - 只運行單元測試");
    Print("3. RunPipelineAdvanceIntegrationTests()   - 只運行整合測試");
    Print("4. QuickPipelineAdvanceCheck()            - 快速檢查（靜默模式）");
    Print("");
    Print("特定測試類別:");
    Print("5. RunSpecificPipelineAdvanceTest(\"TestPipelineResult\")");
    Print("6. RunSpecificPipelineAdvanceTest(\"TestPipelineComposite\")");
    Print("7. RunSpecificPipelineAdvanceTest(\"TestPipelineStates\")");
    Print("8. RunSpecificPipelineAdvanceTest(\"TestPipelineIntegration\")");
    Print("");
    Print("使用方法: 在EA的OnInit()或OnTick()中調用相應的函數");
    Print("例如: RunAllPipelineAdvanceTests();");
}

//+------------------------------------------------------------------+
//| 驗證測試環境函數                                                 |
//+------------------------------------------------------------------+
bool ValidateTestEnvironment()
{
    Print("🔍 驗證測試環境...");

    // 檢查必要的檔案是否存在（這裡只是示例，實際MQL4中無法直接檢查檔案）
    bool environmentValid = true;

    // 檢查測試框架
    TestRunner* testRunner = new TestRunner();
    if(testRunner == NULL)
    {
        Print("❌ 測試框架初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete testRunner;
        Print("✅ 測試框架正常");
    }

    // 檢查Mock類別
    MockPipeline* mockPipeline = new MockPipeline("環境測試");
    if(mockPipeline == NULL)
    {
        Print("❌ Mock類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete mockPipeline;
        Print("✅ Mock類別正常");
    }

    // 檢查被測試的類別
    PipelineComposite* composite = new PipelineComposite("環境測試", "EnvTest");
    if(composite == NULL)
    {
        Print("❌ PipelineComposite類別初始化失敗");
        environmentValid = false;
    }
    else
    {
        delete composite;
        Print("✅ PipelineComposite類別正常");
    }

    if(environmentValid)
    {
        Print("🎉 測試環境驗證通過！");
    }
    else
    {
        Print("❌ 測試環境驗證失敗，請檢查相關檔案和依賴");
    }

    return environmentValid;
}

//+------------------------------------------------------------------+
//| 性能測試函數                                                     |
//+------------------------------------------------------------------+
void RunPerformanceTests()
{
    Print("🚀 開始性能測試...");

    uint startTime = GetTickCount();

    // 運行快速檢查
    bool result = QuickPipelineAdvanceCheck();

    uint endTime = GetTickCount();
    uint duration = endTime - startTime;

    Print("⏱️ 性能測試結果:");
    Print("   執行時間: " + IntegerToString(duration) + " 毫秒");
    Print("   測試結果: " + (result ? "通過" : "失敗"));

    if(duration < 1000)
    {
        Print("✅ 性能優秀 (< 1秒)");
    }
    else if(duration < 5000)
    {
        Print("⚠️ 性能一般 (1-5秒)");
    }
    else
    {
        Print("❌ 性能較差 (> 5秒)");
    }
}

//+------------------------------------------------------------------+
//| 回歸測試函數 - 用於確保修改後功能仍然正常                         |
//+------------------------------------------------------------------+
void RunRegressionTests()
{
    Print("🔄 開始回歸測試...");
    Print("這將運行所有測試以確保沒有功能退化...");

    bool previousResult = QuickPipelineAdvanceCheck();

    Print("📊 回歸測試結果:");
    if(previousResult)
    {
        Print("✅ 回歸測試通過 - 所有功能正常");
        Print("   可以安全地部署新版本");
    }
    else
    {
        Print("❌ 回歸測試失敗 - 發現功能退化");
        Print("   請檢查最近的修改並修復問題");
    }
}

//+------------------------------------------------------------------+
//| 測試報告生成函數                                                 |
//+------------------------------------------------------------------+
void GenerateTestReport()
{
    Print("📋 生成測試報告...");

    PipelineAdvanceTestRunner* runner = new PipelineAdvanceTestRunner();
    runner.RunAllTests();

    Print("");
    Print("📊 詳細測試報告:");
    Print("   總測試數: " + IntegerToString(runner.GetTotalTests()));
    Print("   通過測試: " + IntegerToString(runner.GetPassedTests()));
    Print("   失敗測試: " + IntegerToString(runner.GetFailedTests()));
    Print("   成功率: " + DoubleToString(
        runner.GetTotalTests() > 0 ?
        (double)runner.GetPassedTests() / runner.GetTotalTests() * 100.0 : 0.0, 2) + "%");

    Print("");
    Print("📅 報告生成時間: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
    Print("🏷️ 模組版本: PipelineAdvance v1.0");
    Print("🔧 測試框架: EA_Wizard TestFramework v1.0");

    delete runner;
}

//+------------------------------------------------------------------+
//| 開發者測試函數 - 用於開發過程中的快速測試                         |
//+------------------------------------------------------------------+
void DevQuickTest()
{
    Print("🛠️ 開發者快速測試...");

    // 只運行最關鍵的測試
    RunSpecificPipelineAdvanceTest("TestPipelineResult");
    RunSpecificPipelineAdvanceTest("TestPipelineComposite");

    Print("✅ 開發者快速測試完成");
}

//+------------------------------------------------------------------+
//| 文檔輸出功能函數                                                 |
//+------------------------------------------------------------------+

// 運行所有測試並生成完整文檔
void RunAllPipelineAdvanceTestsWithDocs()
{
    Print("📄 開始執行 PipelineAdvance 測試並生成文檔...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 測試執行完成，文檔已生成");
}

// 運行單元測試並生成文檔
void RunPipelineAdvanceUnitTestsWithDocs()
{
    Print("📄 開始執行 PipelineAdvance 單元測試並生成文檔...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs(true, false);
    runner.RunUnitTestsOnlyWithDocs();
    delete runner;

    Print("✅ 單元測試執行完成，文檔已生成");
}

// 運行整合測試並生成文檔
void RunPipelineAdvanceIntegrationTestsWithDocs()
{
    Print("📄 開始執行 PipelineAdvance 整合測試並生成文檔...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs(false, true);
    runner.RunIntegrationTestsOnlyWithDocs();
    delete runner;

    Print("✅ 整合測試執行完成，文檔已生成");
}

// 運行特定測試並生成文檔
void RunSpecificPipelineAdvanceTestWithDocs(string testClassName)
{
    Print("📄 開始執行 " + testClassName + " 測試並生成文檔...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.RunSpecificTestWithDocs(testClassName);
    delete runner;

    Print("✅ " + testClassName + " 測試執行完成，文檔已生成");
}

// 生成測試文檔（不重新運行測試）
void GeneratePipelineAdvanceTestDocuments()
{
    Print("📄 重新生成 PipelineAdvance 測試文檔...");

    // 運行快速測試以獲取結果
    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs(true, true, false);
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 測試文檔已重新生成");
}

// 自定義文檔輸出選項
void RunPipelineAdvanceTestsWithCustomDocs(bool generateFullReport = true,
                                           bool generateSummary = true,
                                           string outputDirectory = "PipelineAdvance_TestReports")
{
    Print("📄 開始執行自定義文檔輸出的 PipelineAdvance 測試...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(generateFullReport, generateSummary, outputDirectory);
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 自定義測試文檔已生成到: " + outputDirectory);
}

// 自定義文檔輸出選項（包含通過測試顯示設置）
void RunPipelineAdvanceTestsWithAdvancedDocs(bool generateFullReport = true,
                                             bool generateSummary = true,
                                             string outputDirectory = "PipelineAdvance_TestReports",
                                             int maxPassedTestsDisplay = 10)
{
    Print("📄 開始執行高級自定義文檔輸出的 PipelineAdvance 測試...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(generateFullReport, generateSummary, outputDirectory);
    runner.SetPassedTestsDisplayOptions(maxPassedTestsDisplay);
    runner.RunAllTestsWithDocs();
    delete runner;

    string displayInfo = (maxPassedTestsDisplay == -1) ? "無限制" : IntegerToString(maxPassedTestsDisplay);
    Print("✅ 高級自定義測試文檔已生成到: " + outputDirectory);
    Print("   通過測試顯示設置: " + displayInfo);
}

// 運行測試並生成無限制顯示的文檔
void RunPipelineAdvanceTestsWithUnlimitedDisplay(string outputDirectory = "PipelineAdvance_UnlimitedDisplay")
{
    Print("📄 開始執行無限制顯示的 PipelineAdvance 測試...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(true, true, outputDirectory);
    runner.SetUnlimitedPassedTestsDisplay();
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 無限制顯示測試文檔已生成到: " + outputDirectory);
    Print("   所有通過的測試都會完整顯示");
}

// 運行測試並生成限制顯示的文檔
void RunPipelineAdvanceTestsWithLimitedDisplay(int maxDisplay = 5,
                                               string outputDirectory = "PipelineAdvance_LimitedDisplay")
{
    Print("📄 開始執行限制顯示的 PipelineAdvance 測試...");

    PipelineAdvanceTestRunnerWithDocs* runner = new PipelineAdvanceTestRunnerWithDocs();
    runner.SetDocumentOptions(true, true, outputDirectory);
    runner.SetPassedTestsDisplayOptions(maxDisplay);
    runner.RunAllTestsWithDocs();
    delete runner;

    Print("✅ 限制顯示測試文檔已生成到: " + outputDirectory);
    Print("   最多顯示 " + IntegerToString(maxDisplay) + " 個通過的測試");
}

//+------------------------------------------------------------------+
//| 主要導出函數 - 供外部調用                                         |
//+------------------------------------------------------------------+

// 標準測試入口
void TestPipelineAdvance()
{
    RunPipelineAdvanceTests();
}

// 標準測試入口（含文檔輸出）
void TestPipelineAdvanceWithDocs()
{
    RunAllPipelineAdvanceTestsWithDocs();
}

// 完整測試套件
void FullPipelineAdvanceTestSuite()
{
    ValidateTestEnvironment();
    RunPipelineAdvanceTests();
    RunPerformanceTests();
    GenerateTestReport();
}

// 完整測試套件（含文檔輸出）
void FullPipelineAdvanceTestSuiteWithDocs()
{
    Print("🚀 開始執行完整的 PipelineAdvance 測試套件（含文檔輸出）...");

    ValidateTestEnvironment();
    RunAllPipelineAdvanceTestsWithDocs();
    RunPerformanceTests();

    Print("✅ 完整測試套件執行完成，所有文檔已生成");
}

// 持續整合測試
void CIPipelineAdvanceTests()
{
    Print("🔄 持續整合測試開始...");

    if(!ValidateTestEnvironment())
    {
        Print("❌ 環境驗證失敗，終止測試");
        return;
    }

    bool result = QuickPipelineAdvanceCheck();

    if(result)
    {
        Print("✅ CI測試通過 - 構建可以繼續");
    }
    else
    {
        Print("❌ CI測試失敗 - 構建應該停止");
    }
}
