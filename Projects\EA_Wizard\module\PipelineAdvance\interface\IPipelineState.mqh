//+------------------------------------------------------------------+
//|                                             IPipelineState.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../enum/PipelineStateEnum.mqh"

//+------------------------------------------------------------------+
//| 流水線狀態介面 - 定義流水線狀態的基本行為                         |
//+------------------------------------------------------------------+
template <typename PipelineObj>
interface IPipelineState
{
public:
    // 執行流水線
    void Execute(PipelineObj pipeline);
    
    // 重置流水線狀態
    void Restore(PipelineObj pipeline);
    
    // 獲取狀態類型
    ENUM_PIPELINE_STATE GetState();
    
    // 獲取狀態描述
    string GetStateDescription();
};
